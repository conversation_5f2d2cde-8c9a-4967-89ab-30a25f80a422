@page "/test/pagination"
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Models.Common
@using NafaPlace.Web.Services
@inject IProductService ProductService
@inject ICategoryService CategoryService

<PageTitle>Test de Pagination - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title">
                <i class="bi bi-grid-3x3-gap text-primary me-3"></i>
                Test des Composants de Pagination
            </h1>
            <p class="text-muted">Cette page permet de tester les différents composants de pagination implémentés.</p>
        </div>
    </div>

    <!-- Test du composant Pagination simple -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Composant Pagination Simple</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Test du composant de pagination de base avec différentes configurations.</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Pagination Standard</h6>
                            <Pagination CurrentPage="@currentPage1" 
                                        TotalPages="@totalPages1" 
                                        TotalItems="@totalItems1"
                                        OnPageChange="OnPage1Change" />
                        </div>
                        <div class="col-md-6">
                            <h6>Pagination Compacte (sans icônes)</h6>
                            <Pagination CurrentPage="@currentPage2" 
                                        TotalPages="@totalPages2" 
                                        TotalItems="@totalItems2"
                                        ShowIcons="false"
                                        MaxVisiblePages="3"
                                        OnPageChange="OnPage2Change" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test du composant PaginationControls -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Composant PaginationControls Complet</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Test du composant de pagination avec contrôles de taille de page et informations.</p>
                    
                    <PaginationControls CurrentPage="@currentPage3" 
                                        TotalPages="@totalPages3" 
                                        TotalItems="@totalItems3"
                                        PageSize="@pageSize3"
                                        PageSizeOptions="@(new[] { 5, 10, 20, 50 })"
                                        ShowPageSizeSelector="true"
                                        AriaLabel="Test de pagination complète"
                                        OnPageChange="OnPage3Change"
                                        OnPageSizeChange="OnPageSize3Change" />
                </div>
            </div>
        </div>
    </div>

    <!-- Test du composant ProductSection -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Composant ProductSection avec Pagination</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Test du composant de section de produits avec pagination intégrée.</p>
                    
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <ProductSection Title="Produits en Vedette" 
                                            SectionType="featured"
                                            ViewAllUrl="/catalog"
                                            ColumnClass="col-6"
                                            InitialPageSize="4"
                                            PageSizeOptions="@(new[] { 4, 8, 12 })"
                                            ShowQuickActions="true" />
                        </div>
                        <div class="col-lg-6 mb-4">
                            <ProductSection Title="Nouveautés" 
                                            SectionType="new"
                                            ViewAllUrl="/nouveautes"
                                            ColumnClass="col-6"
                                            InitialPageSize="4"
                                            PageSizeOptions="@(new[] { 4, 8, 12 })"
                                            ShowQuickActions="true" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contrôles de test -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Contrôles de Test</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>Pagination 1</h6>
                            <p>Page courante: <strong>@currentPage1</strong> / @totalPages1</p>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-primary btn-sm" @onclick="() => SetTestData1(5, 50)">5 pages</button>
                                <button class="btn btn-outline-primary btn-sm" @onclick="() => SetTestData1(15, 150)">15 pages</button>
                                <button class="btn btn-outline-primary btn-sm" @onclick="() => SetTestData1(100, 1000)">100 pages</button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>Pagination 2</h6>
                            <p>Page courante: <strong>@currentPage2</strong> / @totalPages2</p>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-secondary btn-sm" @onclick="() => SetTestData2(3, 30)">3 pages</button>
                                <button class="btn btn-outline-secondary btn-sm" @onclick="() => SetTestData2(7, 70)">7 pages</button>
                                <button class="btn btn-outline-secondary btn-sm" @onclick="() => SetTestData2(25, 250)">25 pages</button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>Pagination 3</h6>
                            <p>Page courante: <strong>@currentPage3</strong> / @totalPages3</p>
                            <p>Taille de page: <strong>@pageSize3</strong></p>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-success btn-sm" @onclick="() => SetTestData3(10, 200, 20)">10 pages</button>
                                <button class="btn btn-outline-success btn-sm" @onclick="() => SetTestData3(50, 1000, 20)">50 pages</button>
                                <button class="btn btn-outline-success btn-sm" @onclick="() => SetTestData3(200, 5000, 25)">200 pages</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // Pagination 1
    private int currentPage1 = 1;
    private int totalPages1 = 10;
    private int totalItems1 = 100;

    // Pagination 2
    private int currentPage2 = 1;
    private int totalPages2 = 5;
    private int totalItems2 = 50;

    // Pagination 3
    private int currentPage3 = 1;
    private int totalPages3 = 20;
    private int totalItems3 = 400;
    private int pageSize3 = 20;

    private async Task OnPage1Change(int page)
    {
        currentPage1 = page;
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnPage2Change(int page)
    {
        currentPage2 = page;
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnPage3Change(int page)
    {
        currentPage3 = page;
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnPageSize3Change(int pageSize)
    {
        pageSize3 = pageSize;
        totalPages3 = (int)Math.Ceiling((double)totalItems3 / pageSize3);
        currentPage3 = 1;
        await InvokeAsync(StateHasChanged);
    }

    private void SetTestData1(int pages, int items)
    {
        totalPages1 = pages;
        totalItems1 = items;
        currentPage1 = 1;
        StateHasChanged();
    }

    private void SetTestData2(int pages, int items)
    {
        totalPages2 = pages;
        totalItems2 = items;
        currentPage2 = 1;
        StateHasChanged();
    }

    private void SetTestData3(int pages, int items, int pageSize)
    {
        totalPages3 = pages;
        totalItems3 = items;
        pageSize3 = pageSize;
        currentPage3 = 1;
        StateHasChanged();
    }
}
