@using Microsoft.AspNetCore.Components

<nav aria-label="@AriaLabel" class="@CssClass">
    <ul class="pagination @PaginationClass">
        <!-- Bouton Précédent -->
        <li class="page-item @(CurrentPage == 1 ? "disabled" : "")">
            <button class="page-link" @onclick="() => OnPageChanged(CurrentPage - 1)" 
                    disabled="@(CurrentPage == 1)" aria-label="Page précédente">
                @if (ShowIcons)
                {
                    <i class="bi bi-chevron-left"></i>
                }
                else
                {
                    <span aria-hidden="true">&laquo;</span>
                }
            </button>
        </li>

        @if (TotalPages <= MaxVisiblePages)
        {
            <!-- Afficher toutes les pages si le nombre total est petit -->
            @for (int i = 1; i <= TotalPages; i++)
            {
                var pageNumber = i;
                <li class="page-item @(CurrentPage == pageNumber ? "active" : "")">
                    <button class="page-link" @onclick="() => OnPageChanged(pageNumber)">
                        @pageNumber
                        @if (CurrentPage == pageNumber)
                        {
                            <span class="visually-hidden">(page courante)</span>
                        }
                    </button>
                </li>
            }
        }
        else
        {
            <!-- Logique de pagination avancée pour beaucoup de pages -->
            @{
                var startPage = Math.Max(1, CurrentPage - (MaxVisiblePages / 2));
                var endPage = Math.Min(TotalPages, startPage + MaxVisiblePages - 1);
                
                // Ajuster le début si on est proche de la fin
                if (endPage - startPage + 1 < MaxVisiblePages)
                {
                    startPage = Math.Max(1, endPage - MaxVisiblePages + 1);
                }
            }

            <!-- Première page et ellipses si nécessaire -->
            @if (startPage > 1)
            {
                <li class="page-item">
                    <button class="page-link" @onclick="() => OnPageChanged(1)">1</button>
                </li>
                @if (startPage > 2)
                {
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                }
            }

            <!-- Pages visibles -->
            @for (int i = startPage; i <= endPage; i++)
            {
                var pageNumber = i;
                <li class="page-item @(CurrentPage == pageNumber ? "active" : "")">
                    <button class="page-link" @onclick="() => OnPageChanged(pageNumber)">
                        @pageNumber
                        @if (CurrentPage == pageNumber)
                        {
                            <span class="visually-hidden">(page courante)</span>
                        }
                    </button>
                </li>
            }

            <!-- Ellipses et dernière page si nécessaire -->
            @if (endPage < TotalPages)
            {
                @if (endPage < TotalPages - 1)
                {
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                }
                <li class="page-item">
                    <button class="page-link" @onclick="() => OnPageChanged(TotalPages)">@TotalPages</button>
                </li>
            }
        }

        <!-- Bouton Suivant -->
        <li class="page-item @(CurrentPage == TotalPages ? "disabled" : "")">
            <button class="page-link" @onclick="() => OnPageChanged(CurrentPage + 1)" 
                    disabled="@(CurrentPage == TotalPages)" aria-label="Page suivante">
                @if (ShowIcons)
                {
                    <i class="bi bi-chevron-right"></i>
                }
                else
                {
                    <span aria-hidden="true">&raquo;</span>
                }
            </button>
        </li>
    </ul>
</nav>

@if (ShowInfo)
{
    <div class="pagination-info text-center text-muted mt-2">
        <small>
            Page @CurrentPage sur @TotalPages
            @if (TotalItems > 0)
            {
                <span> - @TotalItems élément@(TotalItems > 1 ? "s" : "") au total</span>
            }
        </small>
    </div>
}

@code {
    [Parameter] public int CurrentPage { get; set; } = 1;
    [Parameter] public int TotalPages { get; set; } = 1;
    [Parameter] public int TotalItems { get; set; } = 0;
    [Parameter] public int MaxVisiblePages { get; set; } = 5;
    [Parameter] public bool ShowInfo { get; set; } = true;
    [Parameter] public bool ShowIcons { get; set; } = true;
    [Parameter] public string CssClass { get; set; } = "";
    [Parameter] public string PaginationClass { get; set; } = "justify-content-center";
    [Parameter] public string AriaLabel { get; set; } = "Navigation des pages";
    [Parameter] public EventCallback<int> OnPageChange { get; set; }

    private async Task OnPageChanged(int page)
    {
        if (page >= 1 && page <= TotalPages && page != CurrentPage)
        {
            await OnPageChange.InvokeAsync(page);
        }
    }
}
