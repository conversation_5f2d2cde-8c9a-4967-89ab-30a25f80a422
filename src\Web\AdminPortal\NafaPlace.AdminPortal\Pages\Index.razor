@page "/"
@using Microsoft.AspNetCore.Components.Authorization
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@code {
    protected override async Task OnInitializedAsync()
    {
        // Vérifier si l'utilisateur est authentifié
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            // Si authentifié, rediriger vers le dashboard
            NavigationManager.NavigateTo("/dashboard", true);
        }
        else
        {
            // Si non authentifié, rediriger vers la page de login
            NavigationManager.NavigateTo("/login", true);
        }
    }
}
