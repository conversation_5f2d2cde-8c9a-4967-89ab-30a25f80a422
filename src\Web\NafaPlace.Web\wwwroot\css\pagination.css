/* Styles pour la pagination */
.pagination-controls {
    margin-top: 2rem;
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
}

.pagination-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.page-size-selector {
    font-size: 0.9rem;
}

.page-size-selector .form-select {
    min-width: 80px;
}

/* Styles pour la pagination Bootstrap personnalisés */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    color: var(--primary, #E73C30);
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    transition: all 0.2s ease-in-out;
}

.pagination .page-link:hover {
    color: #fff;
    background-color: var(--primary, #E73C30);
    border-color: var(--primary, #E73C30);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary, #E73C30);
    border-color: var(--primary, #E73C30);
    color: #fff;
    box-shadow: 0 2px 4px rgba(231, 60, 48, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination .page-item.disabled .page-link:hover {
    transform: none;
}

/* Pagination mobile */
@media (max-width: 768px) {
    .pagination-controls {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .pagination-info {
        order: 2;
    }
    
    .page-size-selector {
        order: 1;
        justify-content: center;
    }
    
    .pagination {
        justify-content: center !important;
    }
    
    .pagination .page-link {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    /* Masquer certains numéros de page sur mobile */
    .pagination-mobile .page-item:not(.active):not(:first-child):not(:last-child):not(:nth-child(2)):not(:nth-last-child(2)) {
        display: none;
    }
}

/* Styles pour les sections de produits */
.product-section {
    margin-bottom: 3rem;
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.section-title {
    color: var(--dark, #003366);
    font-weight: 600;
    margin: 0;
}

.section-title i {
    margin-right: 0.5rem;
}

/* Badges pour les produits */
.badge-new {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-featured {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(45deg, var(--primary, #E73C30), var(--secondary, #F96302));
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-sale {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Prix avec remise */
.price-with-discount {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.current-price {
    color: var(--primary, #E73C30);
    font-weight: 700;
    font-size: 1.1rem;
}

.original-price {
    color: #6c757d;
    font-size: 0.9rem;
    text-decoration: line-through;
}

/* État vide */
.empty-state {
    padding: 3rem 1rem;
}

.empty-state i {
    color: #dee2e6;
    font-size: 4rem;
}

.empty-state h3 {
    color: #6c757d;
    margin-top: 1rem;
}

/* Animation de chargement */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive pour les cartes produits */
@media (max-width: 576px) {
    .product-card .card-body {
        padding: 0.75rem;
    }
    
    .product-card .card-title {
        font-size: 0.9rem;
        line-height: 1.3;
    }
    
    .current-price {
        font-size: 1rem;
    }
    
    .card-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
}

/* Amélioration de l'accessibilité */
.pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(231, 60, 48, 0.25);
    outline: none;
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(231, 60, 48, 0.25);
    outline: none;
}

/* Animation pour les cartes produits */
.product-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Indicateur de page courante amélioré */
.pagination .page-item.active .page-link {
    position: relative;
}

.pagination .page-item.active .page-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background-color: var(--primary, #E73C30);
    border-radius: 50%;
}
