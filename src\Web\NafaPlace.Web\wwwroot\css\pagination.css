/* Styles pour la pagination */
.pagination-controls {
    margin-top: 2rem;
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
}

.pagination-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.page-size-selector {
    font-size: 0.9rem;
}

.page-size-selector .form-select {
    min-width: 80px;
}

/* Styles pour la pagination Bootstrap personnalisés */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    color: var(--primary, #E73C30);
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    transition: all 0.2s ease-in-out;
}

.pagination .page-link:hover {
    color: #fff;
    background-color: var(--primary, #E73C30);
    border-color: var(--primary, #E73C30);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary, #E73C30);
    border-color: var(--primary, #E73C30);
    color: #fff;
    box-shadow: 0 2px 4px rgba(231, 60, 48, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination .page-item.disabled .page-link:hover {
    transform: none;
}

/* Pagination mobile */
@media (max-width: 768px) {
    .pagination-controls {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .pagination-info {
        order: 2;
    }
    
    .page-size-selector {
        order: 1;
        justify-content: center;
    }
    
    .pagination {
        justify-content: center !important;
    }
    
    .pagination .page-link {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    /* Masquer certains numéros de page sur mobile */
    .pagination-mobile .page-item:not(.active):not(:first-child):not(:last-child):not(:nth-child(2)):not(:nth-last-child(2)) {
        display: none;
    }
}

/* Styles pour les sections de produits */
.product-section {
    margin-bottom: 3rem;
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.section-title {
    color: var(--dark, #003366);
    font-weight: 600;
    margin: 0;
}

.section-title i {
    margin-right: 0.5rem;
}

/* Badges pour les produits */
.badge-new {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-featured {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(45deg, var(--primary, #E73C30), var(--secondary, #F96302));
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-sale {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Prix avec remise */
.price-with-discount {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.current-price {
    color: var(--primary, #E73C30);
    font-weight: 700;
    font-size: 1.1rem;
}

.original-price {
    color: #6c757d;
    font-size: 0.9rem;
    text-decoration: line-through;
}

/* État vide */
.empty-state {
    padding: 3rem 1rem;
}

.empty-state i {
    color: #dee2e6;
    font-size: 4rem;
}

.empty-state h3 {
    color: #6c757d;
    margin-top: 1rem;
}

/* Animation de chargement */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive pour les cartes produits */
@media (max-width: 576px) {
    .product-card .card-body {
        padding: 0.75rem;
    }
    
    .product-card .card-title {
        font-size: 0.9rem;
        line-height: 1.3;
    }
    
    .current-price {
        font-size: 1rem;
    }
    
    .card-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
}

/* Amélioration de l'accessibilité */
.pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(231, 60, 48, 0.25);
    outline: none;
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(231, 60, 48, 0.25);
    outline: none;
}

/* Animation pour les cartes produits */
.product-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Indicateur de page courante amélioré */
.pagination .page-item.active .page-link {
    position: relative;
}

.pagination .page-item.active .page-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background-color: var(--primary, #E73C30);
    border-radius: 50%;
}

/* ===== STYLES POUR LE CAROUSEL DE PRODUITS ===== */

.product-carousel {
    margin-bottom: 3rem;
}

.carousel-container {
    position: relative;
    padding: 0 50px;
}

.carousel-wrapper {
    overflow: hidden;
    border-radius: 8px;
}

.carousel-track {
    transition: transform 0.3s ease-in-out;
}

.carousel-item {
    padding: 0 8px;
}

/* Boutons de navigation du carousel */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary, #E73C30);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

.carousel-nav:hover:not(.disabled) {
    background: var(--primary, #E73C30);
    color: white;
    transform: translateY(-50%) scale(1.1);
}

.carousel-nav.disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.carousel-nav-prev {
    left: 10px;
}

.carousel-nav-next {
    right: 10px;
}

/* Indicateurs de pagination du carousel */
.carousel-indicators {
    gap: 8px;
    margin-top: 1rem;
}

.carousel-dot {
    width: 10px;
    height: 10px;
    border: none;
    border-radius: 50%;
    background: #dee2e6;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-dot.active,
.carousel-dot:hover {
    background: var(--primary, #E73C30);
    transform: scale(1.2);
}

/* Responsive pour le carousel */
@media (max-width: 1200px) {
    .carousel-container {
        padding: 0 40px;
    }
}

@media (max-width: 992px) {
    /* Tablette : 3 items par slide */
    .product-carousel .carousel-item {
        width: 33.333% !important;
    }
}

@media (max-width: 768px) {
    .carousel-container {
        padding: 0 30px;
    }

    .carousel-nav {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .carousel-nav-prev {
        left: 5px;
    }

    .carousel-nav-next {
        right: 5px;
    }

    .carousel-item {
        padding: 0 4px;
    }
}

@media (max-width: 576px) {
    /* Mobile : 2 items par slide */
    .product-carousel .carousel-item {
        width: 50% !important;
    }

    .carousel-container {
        padding: 0 25px;
    }

    .carousel-nav {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }

    /* Masquer les indicateurs sur très petit écran */
    .product-carousel .carousel-indicators {
        display: none;
    }
}

@media (max-width: 400px) {
    /* Très petit mobile : 1 item par slide */
    .product-carousel .carousel-item {
        width: 100% !important;
    }
}

/* Animation pour les cartes dans le carousel */
.product-carousel .product-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100%;
}

.product-carousel .product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Styles pour les prix dans le carousel */
.product-carousel .current-price {
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary, #E73C30);
}

.product-carousel .original-price {
    font-size: 0.85rem;
    color: #6c757d;
    text-decoration: line-through;
}

/* Boutons d'action dans le carousel */
.product-carousel .card-actions .btn {
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-weight: 500;
}

/* Badges dans le carousel */
.product-carousel .badge-new,
.product-carousel .badge-featured,
.product-carousel .badge-sale {
    font-size: 0.65rem;
    padding: 0.2rem 0.4rem;
}

/* Titre de section du carousel */
.product-carousel .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark, #003366);
    margin: 0;
}

/* État vide du carousel */
.product-carousel .empty-state {
    padding: 2rem 1rem;
    text-align: center;
}

.product-carousel .empty-state i {
    color: #dee2e6;
    font-size: 3rem;
}

/* Chargement du carousel */
.product-carousel .spinner-border {
    width: 2rem;
    height: 2rem;
}

/* ===== STYLES POUR LE PRODUCT GRID (PAGE D'ACCUEIL) ===== */

.product-grid {
    margin-bottom: 3rem;
}

.product-grid .section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.product-grid .section-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--dark, #003366);
    margin: 0;
}

.product-grid .section-subtitle {
    font-size: 1rem;
    margin-top: 0.5rem;
}

/* Cartes produits dans la grille */
.product-grid .product-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    position: relative;
}

.product-grid .product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: var(--primary, #E73C30);
}

/* Images des produits */
.product-grid .card-img-top {
    height: 200px;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.product-grid .product-card:hover .card-img-top {
    transform: scale(1.05);
}

/* Corps de la carte */
.product-grid .card-body {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    height: calc(100% - 200px);
}

.product-grid .card-title {
    font-size: 0.95rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
    height: 2.6rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-grid .card-title a {
    color: #333;
    text-decoration: none;
}

.product-grid .card-title a:hover {
    color: var(--primary, #E73C30);
}

/* Prix dans la grille */
.product-grid .price-section {
    margin-top: auto;
    margin-bottom: 0.75rem;
}

.product-grid .current-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary, #E73C30);
}

.product-grid .original-price {
    font-size: 0.9rem;
    color: #6c757d;
    text-decoration: line-through;
    margin-left: 0.5rem;
}

/* Boutons d'action */
.product-grid .card-actions {
    margin-top: auto;
}

.product-grid .card-actions .btn {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.product-grid .card-actions .btn:hover {
    transform: translateY(-1px);
}

/* Badges dans la grille */
.product-grid .badge-new,
.product-grid .badge-featured,
.product-grid .badge-sale {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-grid .badge-new {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.product-grid .badge-featured {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: white;
}

.product-grid .badge-sale {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
    top: 8px;
    right: 8px;
    left: auto;
}

/* Évaluations */
.product-grid .d-flex {
    align-items: center;
    margin-bottom: 0.5rem;
}

.product-grid .small {
    font-size: 0.8rem;
}

/* Bouton "Voir plus" */
.product-grid .btn-outline-primary {
    border-width: 2px;
    font-weight: 500;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.product-grid .btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 60, 48, 0.3);
}

/* Responsive pour la grille */
@media (max-width: 768px) {
    .product-grid .section-title {
        font-size: 1.5rem;
    }

    .product-grid .card-img-top {
        height: 180px;
    }

    .product-grid .card-body {
        padding: 0.75rem;
        height: calc(100% - 180px);
    }

    .product-grid .card-title {
        font-size: 0.9rem;
        height: 2.4rem;
    }

    .product-grid .current-price {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .product-grid .card-img-top {
        height: 160px;
    }

    .product-grid .card-body {
        padding: 0.5rem;
        height: calc(100% - 160px);
    }

    .product-grid .card-title {
        font-size: 0.85rem;
        height: 2.2rem;
    }

    .product-grid .card-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* ===== STYLES POUR LE COMPOSANT PRODUCTCARD ===== */

.product-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    background: white;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: var(--primary, #E73C30);
}

.product-image-container {
    position: relative;
    overflow: hidden;
}

.product-card .card-img-top {
    height: 200px;
    width: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.stock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

.stock-badge {
    background: #dc3545;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
}

.product-card .card-body {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 200px;
}

.product-card .card-title {
    font-size: 0.95rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
    height: 2.6rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-title-link {
    color: #333;
    text-decoration: none;
}

.product-title-link:hover {
    color: var(--primary, #E73C30);
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars {
    font-size: 0.8rem;
}

.rating-count {
    color: #6c757d;
    font-size: 0.8rem;
}

.product-category {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.product-card .price-section {
    margin-top: auto;
    margin-bottom: 0.75rem;
}

.product-card .current-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary, #E73C30);
}

.product-card .original-price {
    font-size: 0.9rem;
    color: #6c757d;
    text-decoration: line-through;
    margin-left: 0.5rem;
}

.savings {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 500;
    margin-top: 0.25rem;
}

.product-card .card-actions {
    margin-top: auto;
}

.product-card .card-actions .btn {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.product-card .card-actions .btn:hover {
    transform: translateY(-1px);
}

/* Badges pour ProductCard */
.product-card .badge-new,
.product-card .badge-featured,
.product-card .badge-sale {
    position: absolute;
    top: 8px;
    z-index: 2;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-card .badge-new {
    left: 8px;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.product-card .badge-sale {
    right: 8px;
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
}

/* Responsive pour ProductCard */
@media (max-width: 768px) {
    .product-card .card-img-top {
        height: 180px;
    }

    .product-card .card-body {
        padding: 0.75rem;
        min-height: 180px;
    }

    .product-card .card-title {
        font-size: 0.9rem;
        height: 2.4rem;
    }

    .product-card .current-price {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .product-card .card-img-top {
        height: 160px;
    }

    .product-card .card-body {
        padding: 0.5rem;
        min-height: 160px;
    }

    .product-card .card-title {
        font-size: 0.85rem;
        height: 2.2rem;
    }

    .product-card .card-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* ===== AMÉLIORATIONS GÉNÉRALES POUR LES PRODUITS ===== */

/* Animation de chargement pour les cartes produits */
.product-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Amélioration des boutons d'action */
.product-card .btn-primary {
    background: linear-gradient(135deg, var(--primary, #E73C30), var(--secondary, #F96302));
    border: none;
    box-shadow: 0 2px 8px rgba(231, 60, 48, 0.3);
}

.product-card .btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary, #F96302), var(--primary, #E73C30));
    box-shadow: 0 4px 12px rgba(231, 60, 48, 0.4);
    transform: translateY(-2px);
}

.product-card .btn-outline-primary {
    border-color: var(--primary, #E73C30);
    color: var(--primary, #E73C30);
}

.product-card .btn-outline-primary:hover {
    background-color: var(--primary, #E73C30);
    border-color: var(--primary, #E73C30);
    color: white;
    transform: translateY(-1px);
}

/* Amélioration des étoiles de notation */
.rating-stars .bi-star-fill {
    color: #ffc107;
}

.rating-stars .bi-star {
    color: #e9ecef;
}

/* Effet de survol sur les images */
.product-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    transition: background 0.3s ease;
    pointer-events: none;
}

.product-card:hover .product-image-container::after {
    background: rgba(0, 0, 0, 0.1);
}

/* Amélioration des badges de réduction */
.product-card .badge-sale {
    background: linear-gradient(135deg, #dc3545, #c82333);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Amélioration des prix */
.product-card .price-with-discount {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.product-card .current-price {
    background: linear-gradient(135deg, var(--primary, #E73C30), var(--secondary, #F96302));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Amélioration des sections */
.product-grid .section-header {
    position: relative;
    padding-bottom: 1.5rem;
}

.product-grid .section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary, #E73C30), var(--secondary, #F96302));
    border-radius: 2px;
}

/* Amélioration des boutons "Voir tout" */
.product-grid .btn-outline-primary {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.product-grid .btn-outline-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary, #E73C30), var(--secondary, #F96302));
    transition: left 0.3s ease;
    z-index: -1;
}

.product-grid .btn-outline-primary:hover::before {
    left: 0;
}

.product-grid .btn-outline-primary:hover {
    color: white;
    border-color: transparent;
}

/* Responsive amélioré */
@media (max-width: 992px) {
    .product-grid .section-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .product-grid .section-header {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .product-grid .section-header::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .product-grid .btn-outline-primary {
        margin-top: 1rem;
    }
}

/* ===== AMÉLIORATIONS POUR LA PAGE D'ACCUEIL ===== */

/* Espacement entre les sections */
.products-section {
    margin-bottom: 4rem;
}

.products-section:last-child {
    margin-bottom: 2rem;
}

/* Amélioration des alertes */
.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border: 1px solid #b6d4da;
    border-radius: 12px;
    padding: 2rem;
}

.alert-info .bi {
    font-size: 1.5rem;
}

/* Amélioration des titres de section */
.section-title {
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(135deg, var(--primary, #E73C30), var(--secondary, #F96302));
    border-radius: 1px;
    transform: scaleX(0);
    transform-origin: left;
    animation: titleUnderline 0.8s ease-out 0.3s forwards;
}

@keyframes titleUnderline {
    to {
        transform: scaleX(1);
    }
}

/* Animation d'apparition des cartes */
.product-card {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease-out forwards;
}

.product-card:nth-child(1) { animation-delay: 0.1s; }
.product-card:nth-child(2) { animation-delay: 0.2s; }
.product-card:nth-child(3) { animation-delay: 0.3s; }
.product-card:nth-child(4) { animation-delay: 0.4s; }
.product-card:nth-child(5) { animation-delay: 0.5s; }
.product-card:nth-child(6) { animation-delay: 0.6s; }
.product-card:nth-child(7) { animation-delay: 0.7s; }
.product-card:nth-child(8) { animation-delay: 0.8s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Amélioration du responsive */
@media (max-width: 1200px) {
    .products-section {
        margin-bottom: 3rem;
    }
}

@media (max-width: 768px) {
    .products-section {
        margin-bottom: 2.5rem;
    }

    .section-title {
        font-size: 1.4rem;
    }

    .section-subtitle {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .products-section {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.2rem;
    }

    .alert-info {
        padding: 1.5rem;
        text-align: center;
    }
}
