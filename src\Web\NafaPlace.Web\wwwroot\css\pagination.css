/* Styles pour la pagination */
.pagination-controls {
    margin-top: 2rem;
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
}

.pagination-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.page-size-selector {
    font-size: 0.9rem;
}

.page-size-selector .form-select {
    min-width: 80px;
}

/* Styles pour la pagination Bootstrap personnalisés */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    color: var(--primary, #E73C30);
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    transition: all 0.2s ease-in-out;
}

.pagination .page-link:hover {
    color: #fff;
    background-color: var(--primary, #E73C30);
    border-color: var(--primary, #E73C30);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary, #E73C30);
    border-color: var(--primary, #E73C30);
    color: #fff;
    box-shadow: 0 2px 4px rgba(231, 60, 48, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination .page-item.disabled .page-link:hover {
    transform: none;
}

/* Pagination mobile */
@media (max-width: 768px) {
    .pagination-controls {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .pagination-info {
        order: 2;
    }
    
    .page-size-selector {
        order: 1;
        justify-content: center;
    }
    
    .pagination {
        justify-content: center !important;
    }
    
    .pagination .page-link {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    /* Masquer certains numéros de page sur mobile */
    .pagination-mobile .page-item:not(.active):not(:first-child):not(:last-child):not(:nth-child(2)):not(:nth-last-child(2)) {
        display: none;
    }
}

/* Styles pour les sections de produits */
.product-section {
    margin-bottom: 3rem;
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.section-title {
    color: var(--dark, #003366);
    font-weight: 600;
    margin: 0;
}

.section-title i {
    margin-right: 0.5rem;
}

/* Badges pour les produits */
.badge-new {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-featured {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(45deg, var(--primary, #E73C30), var(--secondary, #F96302));
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-sale {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Prix avec remise */
.price-with-discount {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.current-price {
    color: var(--primary, #E73C30);
    font-weight: 700;
    font-size: 1.1rem;
}

.original-price {
    color: #6c757d;
    font-size: 0.9rem;
    text-decoration: line-through;
}

/* État vide */
.empty-state {
    padding: 3rem 1rem;
}

.empty-state i {
    color: #dee2e6;
    font-size: 4rem;
}

.empty-state h3 {
    color: #6c757d;
    margin-top: 1rem;
}

/* Animation de chargement */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive pour les cartes produits */
@media (max-width: 576px) {
    .product-card .card-body {
        padding: 0.75rem;
    }
    
    .product-card .card-title {
        font-size: 0.9rem;
        line-height: 1.3;
    }
    
    .current-price {
        font-size: 1rem;
    }
    
    .card-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
}

/* Amélioration de l'accessibilité */
.pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(231, 60, 48, 0.25);
    outline: none;
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(231, 60, 48, 0.25);
    outline: none;
}

/* Animation pour les cartes produits */
.product-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Indicateur de page courante amélioré */
.pagination .page-item.active .page-link {
    position: relative;
}

.pagination .page-item.active .page-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background-color: var(--primary, #E73C30);
    border-radius: 50%;
}

/* ===== STYLES POUR LE CAROUSEL DE PRODUITS ===== */

.product-carousel {
    margin-bottom: 3rem;
}

.carousel-container {
    position: relative;
    padding: 0 50px;
}

.carousel-wrapper {
    overflow: hidden;
    border-radius: 8px;
}

.carousel-track {
    transition: transform 0.3s ease-in-out;
}

.carousel-item {
    padding: 0 8px;
}

/* Boutons de navigation du carousel */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary, #E73C30);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

.carousel-nav:hover:not(.disabled) {
    background: var(--primary, #E73C30);
    color: white;
    transform: translateY(-50%) scale(1.1);
}

.carousel-nav.disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.carousel-nav-prev {
    left: 10px;
}

.carousel-nav-next {
    right: 10px;
}

/* Indicateurs de pagination du carousel */
.carousel-indicators {
    gap: 8px;
    margin-top: 1rem;
}

.carousel-dot {
    width: 10px;
    height: 10px;
    border: none;
    border-radius: 50%;
    background: #dee2e6;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-dot.active,
.carousel-dot:hover {
    background: var(--primary, #E73C30);
    transform: scale(1.2);
}

/* Responsive pour le carousel */
@media (max-width: 1200px) {
    .carousel-container {
        padding: 0 40px;
    }
}

@media (max-width: 992px) {
    /* Tablette : 3 items par slide */
    .product-carousel .carousel-item {
        width: 33.333% !important;
    }
}

@media (max-width: 768px) {
    .carousel-container {
        padding: 0 30px;
    }

    .carousel-nav {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .carousel-nav-prev {
        left: 5px;
    }

    .carousel-nav-next {
        right: 5px;
    }

    .carousel-item {
        padding: 0 4px;
    }
}

@media (max-width: 576px) {
    /* Mobile : 2 items par slide */
    .product-carousel .carousel-item {
        width: 50% !important;
    }

    .carousel-container {
        padding: 0 25px;
    }

    .carousel-nav {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }

    /* Masquer les indicateurs sur très petit écran */
    .product-carousel .carousel-indicators {
        display: none;
    }
}

@media (max-width: 400px) {
    /* Très petit mobile : 1 item par slide */
    .product-carousel .carousel-item {
        width: 100% !important;
    }
}

/* Animation pour les cartes dans le carousel */
.product-carousel .product-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100%;
}

.product-carousel .product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Styles pour les prix dans le carousel */
.product-carousel .current-price {
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary, #E73C30);
}

.product-carousel .original-price {
    font-size: 0.85rem;
    color: #6c757d;
    text-decoration: line-through;
}

/* Boutons d'action dans le carousel */
.product-carousel .card-actions .btn {
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-weight: 500;
}

/* Badges dans le carousel */
.product-carousel .badge-new,
.product-carousel .badge-featured,
.product-carousel .badge-sale {
    font-size: 0.65rem;
    padding: 0.2rem 0.4rem;
}

/* Titre de section du carousel */
.product-carousel .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark, #003366);
    margin: 0;
}

/* État vide du carousel */
.product-carousel .empty-state {
    padding: 2rem 1rem;
    text-align: center;
}

.product-carousel .empty-state i {
    color: #dee2e6;
    font-size: 3rem;
}

/* Chargement du carousel */
.product-carousel .spinner-border {
    width: 2rem;
    height: 2rem;
}
