using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.DTOs.Product;

namespace NafaPlace.Catalog.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class DiagnosticController : ControllerBase
{
    [HttpPost("test-deserialization")]
    public ActionResult<object> TestDeserialization([FromBody] CreateProductRequest request)
    {
        return Ok(new
        {
            Message = "Test de désérialisation réussi",
            ReceivedFields = new
            {
                // Champs de base
                Name = request.Name ?? "NULL",
                Description = request.Description ?? "NULL",
                Price = request.Price,
                Currency = request.Currency ?? "NULL",
                StockQuantity = request.StockQuantity,
                CategoryId = request.CategoryId,
                SellerId = request.SellerId,
                
                // Champs qui fonctionnent
                Brand = request.Brand ?? "NULL",
                Model = request.Model ?? "NULL",
                
                // Champs problématiques
                Sku = request.Sku ?? "NULL",
                Color = request.Color ?? "NULL",
                Size = request.Size ?? "NULL",
                Material = request.Material ?? "NULL",
                Manufacturer = request.Manufacturer ?? "NULL",
                CountryOfOrigin = request.CountryOfOrigin ?? "NULL",
                WarrantyMonths = request.WarrantyMonths?.ToString() ?? "NULL",
                Features = request.Features ?? "NULL",
                Tags = request.Tags ?? "NULL",
                CompareAtPrice = request.CompareAtPrice?.ToString() ?? "NULL",
                MaxOrderQuantity = request.MaxOrderQuantity?.ToString() ?? "NULL",
                
                // Champs booléens
                RequiresShipping = request.RequiresShipping,
                IsDigital = request.IsDigital,
                IsActive = request.IsActive,
                IsFeatured = request.IsFeatured
            },
            Analysis = new
            {
                TotalPropertiesInRequest = typeof(CreateProductRequest).GetProperties().Length,
                ProblematicFieldsReceived = new
                {
                    SkuReceived = !string.IsNullOrEmpty(request.Sku),
                    ColorReceived = !string.IsNullOrEmpty(request.Color),
                    SizeReceived = !string.IsNullOrEmpty(request.Size),
                    MaterialReceived = !string.IsNullOrEmpty(request.Material),
                    ManufacturerReceived = !string.IsNullOrEmpty(request.Manufacturer),
                    CountryOfOriginReceived = !string.IsNullOrEmpty(request.CountryOfOrigin),
                    WarrantyMonthsReceived = request.WarrantyMonths.HasValue,
                    FeaturesReceived = !string.IsNullOrEmpty(request.Features),
                    TagsReceived = !string.IsNullOrEmpty(request.Tags),
                    CompareAtPriceReceived = request.CompareAtPrice.HasValue,
                    MaxOrderQuantityReceived = request.MaxOrderQuantity.HasValue
                }
            }
        });
    }
}
