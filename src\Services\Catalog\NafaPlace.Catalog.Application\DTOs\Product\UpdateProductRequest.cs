using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Enums;

namespace NafaPlace.Catalog.Application.DTOs.Product;

public class UpdateProductRequest
{
    public required string Name { get; set; }

    public required string Description { get; set; }

    public decimal Price { get; set; }

    public required string Currency { get; set; }

    public int StockQuantity { get; set; }

    public int? CategoryId { get; set; }

    public string? Brand { get; set; }

    public string? Model { get; set; }

    public string? Sku { get; set; }

    public decimal Weight { get; set; }

    public string? Dimensions { get; set; }

    public string? Color { get; set; }

    public string? Size { get; set; }

    public string? Material { get; set; }

    public string? Manufacturer { get; set; }

    public string? CountryOfOrigin { get; set; }

    public string? Condition { get; set; }

    public int? WarrantyMonths { get; set; }

    public string? Features { get; set; }

    public string? Tags { get; set; }

    public decimal? CompareAtPrice { get; set; }

    public DateTime? AvailableFrom { get; set; } = DateTime.UtcNow;

    public int? MinOrderQuantity { get; set; }

    public int? MaxOrderQuantity { get; set; }

    public bool RequiresShipping { get; set; }

    public bool IsDigital { get; set; }

    public bool IsActive { get; set; }
    public bool IsFeatured { get; set; }

    // Champs d'approbation
    public ProductApprovalStatus? ApprovalStatus { get; set; }
    public string? RejectionReason { get; set; }
    public string? ApprovedBy { get; set; }

    public List<UpdateProductImageRequest> Images { get; set; } = new();
    public List<UpdateProductVariantRequest> Variants { get; set; } = new();
    public List<UpdateProductAttributeRequest> Attributes { get; set; } = new();
}

