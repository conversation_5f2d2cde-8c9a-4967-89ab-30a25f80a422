@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Services
@using NafaPlace.Web.Components.Reviews
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IProductService ProductService
@inject ICartService CartService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="product-carousel @CssClass">
    @if (!string.IsNullOrEmpty(Title))
    {
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">
                @if (!string.IsNullOrEmpty(Icon))
                {
                    <i class="@Icon me-2"></i>
                }
                @Title
            </h2>
            @if (!string.IsNullOrEmpty(ViewAllUrl))
            {
                <a href="@ViewAllUrl" class="btn btn-outline-primary">
                    Voir tout <i class="bi bi-arrow-right ms-1"></i>
                </a>
            }
        </div>
    }

    @if (isLoading)
    {
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else if (products == null || !products.Any())
    {
        <div class="empty-state text-center py-4">
            <i class="bi bi-box-seam display-4 text-muted"></i>
            <p class="text-muted mt-2">@EmptyMessage</p>
        </div>
    }
    else
    {
        <div class="carousel-container position-relative">
            <!-- Boutons de navigation -->
            <button class="carousel-nav carousel-nav-prev @(currentIndex == 0 ? "disabled" : "")" 
                    @onclick="PreviousSlide" disabled="@(currentIndex == 0)">
                <i class="bi bi-chevron-left"></i>
            </button>
            <button class="carousel-nav carousel-nav-next @(currentIndex >= maxIndex ? "disabled" : "")" 
                    @onclick="NextSlide" disabled="@(currentIndex >= maxIndex)">
                <i class="bi bi-chevron-right"></i>
            </button>

            <!-- Conteneur des produits -->
            <div class="carousel-wrapper" style="overflow: hidden;">
                <div class="carousel-track d-flex"
                     style="transform: translateX(-@(currentIndex * slideWidth)%); transition: transform 0.3s ease;">
                    @foreach (var product in products)
                    {
                        <div class="carousel-item flex-shrink-0" style="width: @(slideWidth)%;">
                            <div class="card product-card h-100 mx-2">
                                @if (ShowBadges)
                                {
                                    @if (SectionType == "new")
                                    {
                                        <span class="badge-new">NOUVEAU</span>
                                    }
                                    else if (SectionType == "featured")
                                    {
                                        <span class="badge-featured">VEDETTE</span>
                                    }
                                    
                                    @if (product.DiscountPercentage > 0)
                                    {
                                        <span class="badge badge-sale">-@product.DiscountPercentage%</span>
                                    }
                                }

                                <a href="/catalog/products/@product.Id">
                                    <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")" 
                                         class="card-img-top" alt="@product.Name" loading="lazy">
                                </a>

                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">
                                        <a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a>
                                    </h6>
                                    
                                    @if (ShowRatings)
                                    {
                                        <div class="d-flex mb-2">
                                            <StarRating Rating="product.Rating" ShowRatingText="false" CssClass="me-2 small" />
                                            <small class="text-muted">(@product.ReviewCount)</small>
                                        </div>
                                    }
                                    
                                    @if (ShowCategory)
                                    {
                                        <p class="card-text text-muted small">@product.Category?.Name</p>
                                    }
                                    
                                    <div class="price-section mt-auto mb-2">
                                        @if (product.DiscountPercentage > 0 && product.OldPrice.HasValue)
                                        {
                                            <div class="price-with-discount">
                                                <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                                <span class="original-price">@product.OldPrice.Value.ToString("N0") GNF</span>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                        }
                                    </div>

                                    <div class="card-actions">
                                        <button class="btn btn-primary btn-sm w-100" @onclick="() => AddToCart(product.Id)" disabled="@(product.Stock <= 0)">
                                            <i class="bi bi-cart-plus me-1"></i>
                                            @if (product.Stock <= 0)
                                            {
                                                <span>Rupture</span>
                                            }
                                            else
                                            {
                                                <span>Ajouter</span>
                                            }
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Indicateurs de pagination -->
            @if (ShowDots && maxIndex > 0)
            {
                <div class="carousel-indicators d-flex justify-content-center mt-3">
                    @for (int i = 0; i <= maxIndex; i++)
                    {
                        var index = i;
                        <button class="carousel-dot @(currentIndex == index ? "active" : "")" 
                                @onclick="() => GoToSlide(index)"></button>
                    }
                </div>
            }
        </div>
    }
</div>

@code {
    [Parameter] public string Title { get; set; } = "";
    [Parameter] public string Icon { get; set; } = "";
    [Parameter] public string SectionType { get; set; } = "featured"; // "featured", "new", "bestseller"
    [Parameter] public string ViewAllUrl { get; set; } = "";
    [Parameter] public string EmptyMessage { get; set; } = "Aucun produit disponible pour le moment.";
    [Parameter] public string CssClass { get; set; } = "";
    [Parameter] public bool ShowBadges { get; set; } = true;
    [Parameter] public bool ShowRatings { get; set; } = true;
    [Parameter] public bool ShowCategory { get; set; } = false;
    [Parameter] public bool ShowDots { get; set; } = true;
    [Parameter] public int ItemsToShow { get; set; } = 4; // Nombre d'items visibles à la fois (desktop)
    [Parameter] public int ItemsToShowTablet { get; set; } = 3; // Nombre d'items visibles sur tablette
    [Parameter] public int ItemsToShowMobile { get; set; } = 2; // Nombre d'items visibles sur mobile
    [Parameter] public int ItemsToLoad { get; set; } = 12; // Nombre total d'items à charger
    [Parameter] public int? CategoryId { get; set; }
    [Parameter] public string SortBy { get; set; } = "newest";

    private IEnumerable<ProductDto>? products;
    private bool isLoading = true;
    private int currentIndex = 0;
    private string? _userId;
    private int currentItemsToShow = 4;

    // Calculs pour le carousel
    private double slideWidth => 100.0 / currentItemsToShow;
    private int maxIndex => Math.Max(0, (products?.Count() ?? 0) - currentItemsToShow);

    protected override async Task OnInitializedAsync()
    {
        // Récupérer l'utilisateur connecté
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        }

        currentItemsToShow = ItemsToShow;
        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            switch (SectionType.ToLower())
            {
                case "new":
                    products = await ProductService.GetNewProductsAsync(ItemsToLoad);
                    break;
                case "featured":
                default:
                    products = await ProductService.GetFeaturedProductsAsync(ItemsToLoad);
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading products: {ex.Message}");
            products = new List<ProductDto>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void NextSlide()
    {
        if (currentIndex < maxIndex)
        {
            currentIndex++;
            StateHasChanged();
        }
    }

    private void PreviousSlide()
    {
        if (currentIndex > 0)
        {
            currentIndex--;
            StateHasChanged();
        }
    }

    private void GoToSlide(int index)
    {
        if (index >= 0 && index <= maxIndex)
        {
            currentIndex = index;
            StateHasChanged();
        }
    }

    private async Task AddToCart(int productId)
    {
        string userId;
        if (string.IsNullOrEmpty(_userId))
        {
            userId = await GetOrCreateGuestUserId();
        }
        else
        {
            userId = _userId;
        }

        try
        {
            var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };
            var result = await CartService.AddItemToCartAsync(userId, cartItem);
            await JSRuntime.InvokeVoidAsync("showToast", $"✅ Produit ajouté au panier !", "success");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"❌ Erreur: {ex.Message}", "error");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }
        return guestId;
    }
}
