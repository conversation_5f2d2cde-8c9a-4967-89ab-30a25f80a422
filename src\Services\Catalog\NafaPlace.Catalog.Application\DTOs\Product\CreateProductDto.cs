using System;
using System.Collections.Generic;

namespace NafaPlace.Catalog.Application.DTOs.Product
{
    public class CreateProductDto
    {
        public required string Name { get; set; }
        public required string Description { get; set; }
        public decimal Price { get; set; }
        public int CategoryId { get; set; }
        public int StockQuantity { get; set; }
        public required string Currency { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public string? Sku { get; set; }
        public decimal Weight { get; set; }
        public string? Dimensions { get; set; }
        public string? Color { get; set; }
        public string? Size { get; set; }
        public string? Material { get; set; }
        public string? Manufacturer { get; set; }
        public string? CountryOfOrigin { get; set; }
        public string? Condition { get; set; } = "Neuf";
        public int? WarrantyMonths { get; set; }
        public string? Features { get; set; }
        public string? Tags { get; set; }
        public decimal? CompareAtPrice { get; set; }
        public DateTime? AvailableFrom { get; set; }
        public int? MinOrderQuantity { get; set; } = 1;
        public int? MaxOrderQuantity { get; set; }
        public bool RequiresShipping { get; set; } = true;
        public bool IsDigital { get; set; } = false;
        public int SellerId { get; set; }
        public List<CreateProductImageRequest>? Images { get; set; }
        public List<CreateProductVariantRequest>? Variants { get; set; }
        public List<CreateProductAttributeRequest>? Attributes { get; set; }
    }
}
