@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Models.Common
@using NafaPlace.Web.Services
@using NafaPlace.Web.Components.Reviews
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IProductService ProductService
@inject ICartService CartService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="product-section @CssClass">
    @if (!string.IsNullOrEmpty(Title))
    {
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">@Title</h2>
            @if (!string.IsNullOrEmpty(ViewAllUrl))
            {
                <a href="@ViewAllUrl" class="btn btn-outline-primary">
                    Voir tout <i class="bi bi-arrow-right ms-1"></i>
                </a>
            }
        </div>
    }

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-3">Chargement des produits...</p>
        </div>
    }
    else if (pagedResult?.Items == null || !pagedResult.Items.Any())
    {
        <div class="empty-state text-center py-5">
            <i class="bi bi-box-seam display-1 text-muted"></i>
            <h3 class="mt-3">Aucun produit trouvé</h3>
            <p class="text-muted">@EmptyMessage</p>
        </div>
    }
    else
    {
        <div class="row g-4">
            @foreach (var product in pagedResult.Items)
            {
                <div class="@ColumnClass">
                    <div class="card product-card h-100">
                        @if (ShowBadges)
                        {
                            @if (SectionType == "new")
                            {
                                <span class="badge-new">NOUVEAU</span>
                            }
                            else if (SectionType == "featured")
                            {
                                <span class="badge-featured">VEDETTE</span>
                            }
                            
                            @if (product.DiscountPercentage > 0)
                            {
                                <span class="badge badge-sale">-@product.DiscountPercentage%</span>
                            }
                        }

                        <a href="/catalog/products/@product.Id">
                            <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")" 
                                 class="card-img-top" alt="@product.Name">
                        </a>

                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">
                                <a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a>
                            </h6>
                            
                            @if (ShowRatings)
                            {
                                <div class="d-flex mb-2">
                                    <StarRating Rating="product.Rating" ShowRatingText="false" CssClass="me-2" />
                                    <small>(@product.ReviewCount)</small>
                                </div>
                            }
                            
                            @if (ShowCategory)
                            {
                                <p class="card-text text-muted small">@product.Category?.Name</p>
                            }
                            
                            <div class="price-section mt-auto">
                                @if (product.DiscountPercentage > 0 && product.OldPrice.HasValue)
                                {
                                    <div class="price-with-discount">
                                        <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                        <span class="original-price">@product.OldPrice.Value.ToString("N0") GNF</span>
                                    </div>
                                }
                                else
                                {
                                    <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                }
                            </div>

                            <div class="card-actions mt-3">
                                @if (ShowQuickActions)
                                {
                                    <div class="d-flex gap-2">
                                        <a href="/catalog/products/@product.Id" class="btn btn-outline-primary btn-sm flex-fill">
                                            Détails
                                        </a>
                                        <button class="btn btn-primary btn-sm flex-fill" @onclick="() => AddToCart(product.Id)" disabled="@(product.Stock <= 0)">
                                            <i class="bi bi-cart-plus me-1"></i>Ajouter
                                        </button>
                                    </div>
                                }
                                else
                                {
                                    <button class="btn btn-primary btn-sm w-100" @onclick="() => AddToCart(product.Id)" disabled="@(product.Stock <= 0)">
                                        <i class="bi bi-cart-plus me-1"></i>Ajouter au panier
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        @if (ShowPagination && pagedResult.TotalPages > 1)
        {
            <PaginationControls CurrentPage="@currentPage" 
                                TotalPages="@pagedResult.TotalPages" 
                                TotalItems="@pagedResult.TotalCount"
                                PageSize="@pageSize"
                                PageSizeOptions="@PageSizeOptions"
                                ShowPageSizeSelector="@ShowPageSizeSelector"
                                AriaLabel="@($"Navigation {Title}")"
                                OnPageChange="ChangePage"
                                OnPageSizeChange="ChangePageSize" />
        }
    }
</div>

@code {
    [Parameter] public string Title { get; set; } = "";
    [Parameter] public string SectionType { get; set; } = "featured"; // "featured", "new", "bestseller"
    [Parameter] public string ViewAllUrl { get; set; } = "";
    [Parameter] public string EmptyMessage { get; set; } = "Aucun produit disponible pour le moment.";
    [Parameter] public string CssClass { get; set; } = "";
    [Parameter] public string ColumnClass { get; set; } = "col-6 col-md-3";
    [Parameter] public bool ShowBadges { get; set; } = true;
    [Parameter] public bool ShowRatings { get; set; } = true;
    [Parameter] public bool ShowCategory { get; set; } = true;
    [Parameter] public bool ShowQuickActions { get; set; } = false;
    [Parameter] public bool ShowPagination { get; set; } = true;
    [Parameter] public bool ShowPageSizeSelector { get; set; } = true;
    [Parameter] public int InitialPageSize { get; set; } = 8;
    [Parameter] public int[] PageSizeOptions { get; set; } = new[] { 8, 16, 24, 32 };
    [Parameter] public int? CategoryId { get; set; }
    [Parameter] public string SortBy { get; set; } = "newest";

    private PagedResultDto<ProductDto>? pagedResult;
    private bool isLoading = true;
    private int currentPage = 1;
    private int pageSize;
    private string? _userId;

    protected override async Task OnInitializedAsync()
    {
        pageSize = InitialPageSize;
        
        // Récupérer l'utilisateur connecté
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        }

        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            switch (SectionType.ToLower())
            {
                case "new":
                    pagedResult = await ProductService.GetNewProductsPaginatedAsync(currentPage, pageSize, CategoryId, SortBy);
                    break;
                case "featured":
                default:
                    pagedResult = await ProductService.GetFeaturedProductsPaginatedAsync(currentPage, pageSize, CategoryId, SortBy);
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading products: {ex.Message}");
            pagedResult = new PagedResultDto<ProductDto>
            {
                Items = new List<ProductDto>(),
                TotalCount = 0,
                Page = currentPage,
                PageSize = pageSize
            };
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= (pagedResult?.TotalPages ?? 1))
        {
            currentPage = page;
            await LoadProducts();
        }
    }

    private async Task ChangePageSize(int newPageSize)
    {
        pageSize = newPageSize;
        currentPage = 1;
        await LoadProducts();
    }

    private async Task AddToCart(int productId)
    {
        Console.WriteLine($"🔍 DEBUG ProductSection: Tentative d'ajout au panier - UserId: {_userId}, ProductId: {productId}");

        string userId;
        if (string.IsNullOrEmpty(_userId))
        {
            Console.WriteLine("🔍 DEBUG ProductSection: Utilisateur non connecté, utilisation d'un ID invité");
            userId = await GetOrCreateGuestUserId();
        }
        else
        {
            userId = _userId;
        }

        try
        {
            Console.WriteLine($"🛒 DEBUG ProductSection: Création de l'item panier - ProductId: {productId}, Quantity: 1");
            var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };

            Console.WriteLine($"📡 DEBUG ProductSection: Appel API AddItemToCartAsync...");
            var result = await CartService.AddItemToCartAsync(userId, cartItem);

            Console.WriteLine($"✅ DEBUG ProductSection: Produit ajouté avec succès - ItemCount: {result?.ItemCount ?? 0}");

            // Notification de succès
            await JSRuntime.InvokeVoidAsync("showToast", $"✅ Produit ajouté au panier !", "success");
            Console.WriteLine($"Produit {productId} ajouté au panier.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG ProductSection: Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"❌ Erreur: {ex.Message}", "error");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }
}
