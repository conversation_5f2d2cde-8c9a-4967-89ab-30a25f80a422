@page "/catalog/products/{id:int}"
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Models.Cart
@using NafaPlace.Reviews.DTOs
@using NafaPlace.Web.Services
@using NafaPlace.Web.Components.Reviews
@using System.Security.Claims
@inject IProductService ProductService
@inject IReviewService ReviewService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject ICartService CartService
@inject AuthenticationStateProvider AuthenticationStateProvider

<style>
    .product-title {
        font-size: 2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .product-brand {
        font-size: 1.1rem;
        color: #7f8c8d;
    }

    .product-category {
        font-size: 1rem;
        color: #95a5a6;
    }

    .product-sku {
        font-size: 0.9rem;
        color: #bdc3c7;
    }

    .price-with-discount .current-price {
        font-size: 2rem;
        font-weight: 700;
        color: #e74c3c;
    }

    .price-with-discount .original-price {
        font-size: 1.2rem;
        color: #95a5a6;
    }

    .current-price {
        font-size: 2rem;
        font-weight: 700;
        color: #27ae60;
    }

    .product-features ul li {
        margin-bottom: 0.5rem;
        padding-left: 0.5rem;
    }

    .product-specs {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #3498db;
    }

    .variant-buttons .btn {
        border-radius: 20px;
        font-weight: 500;
    }

    .stock-info .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .quantity-selector input {
        text-align: center;
        font-weight: 600;
    }

    .tags-container .badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        border: 1px solid #dee2e6;
    }

    .product-info {
        padding: 1rem 0;
    }

    @@media (max-width: 768px) {
        .product-title {
            font-size: 1.5rem;
        }

        .current-price, .price-with-discount .current-price {
            font-size: 1.5rem;
        }

        .product-specs {
            padding: 0.75rem;
        }
    }
</style>

@if (_product == null)
{
    <p><em>Chargement du produit...</em></p>
}
else
{
    <div class="container my-5">
        <div class="row">
            <div class="col-md-6">
                <img src="@(_product.Images.Any() ? _product.Images.First().Url : "/images/placeholder.png")" class="img-fluid" alt="@_product.Name">
            </div>
            <div class="col-md-6">
                <div class="product-info">
                    <!-- En-tête du produit -->
                    <div class="product-header mb-4">
                        <h1 class="product-title">@_product.Name</h1>
                        @if (!string.IsNullOrEmpty(_product.Brand))
                        {
                            <p class="product-brand text-muted mb-1">
                                <i class="fas fa-tag me-2"></i>@_product.Brand
                                @if (!string.IsNullOrEmpty(_product.Model))
                                {
                                    <span> - @_product.Model</span>
                                }
                            </p>
                        }
                        <p class="product-category text-muted">
                            <i class="fas fa-folder me-2"></i>@_product.Category?.Name
                        </p>
                        @if (!string.IsNullOrEmpty(_product.Sku))
                        {
                            <p class="product-sku text-muted small">
                                <i class="fas fa-barcode me-2"></i>Référence: @_product.Sku
                            </p>
                        }
                    </div>

                    <!-- Prix -->
                    <div class="product-pricing mb-4">
                        @if (_product.CompareAtPrice.HasValue && _product.CompareAtPrice > _product.Price)
                        {
                            <div class="price-with-discount">
                                <h3 class="current-price text-primary mb-1">@((_selectedVariant?.Price ?? _product.Price).ToString("N0")) GNF</h3>
                                <span class="original-price text-muted text-decoration-line-through">@_product.CompareAtPrice.Value.ToString("N0") GNF</span>
                                <span class="badge bg-danger ms-2">
                                    -@(Math.Round(((_product.CompareAtPrice.Value - _product.Price) / _product.CompareAtPrice.Value) * 100))%
                                </span>
                            </div>
                        }
                        else
                        {
                            <h3 class="current-price text-primary mb-1">@((_selectedVariant?.Price ?? _product.Price).ToString("N0")) GNF</h3>
                        }
                    </div>

                    <!-- Description -->
                    <div class="product-description mb-4">
                        <p class="lead">@_product.Description</p>
                        @if (!string.IsNullOrEmpty(_product.Features))
                        {
                            <div class="product-features mt-3">
                                <h6><i class="fas fa-list-ul me-2"></i>Caractéristiques principales :</h6>
                                <ul class="list-unstyled">
                                    @foreach (var feature in _product.Features.Split('\n', StringSplitOptions.RemoveEmptyEntries))
                                    {
                                        <li><i class="fas fa-check text-success me-2"></i>@feature.Trim()</li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>

                    <!-- Informations techniques -->
                    @if (HasTechnicalInfo())
                    {
                        <div class="product-specs mb-4">
                            <h6><i class="fas fa-cogs me-2"></i>Spécifications techniques</h6>
                            <div class="row">
                                @if (!string.IsNullOrEmpty(_product.Color))
                                {
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Couleur:</small><br>
                                        <span>@_product.Color</span>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(_product.Size))
                                {
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Taille:</small><br>
                                        <span>@_product.Size</span>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(_product.Material))
                                {
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Matériau:</small><br>
                                        <span>@_product.Material</span>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(_product.Manufacturer))
                                {
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Fabricant:</small><br>
                                        <span>@_product.Manufacturer</span>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(_product.CountryOfOrigin))
                                {
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Origine:</small><br>
                                        <span>@_product.CountryOfOrigin</span>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(_product.Condition))
                                {
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">État:</small><br>
                                        <span class="badge bg-success">@_product.Condition</span>
                                    </div>
                                }
                                @if (_product.WarrantyMonths.HasValue && _product.WarrantyMonths > 0)
                                {
                                    <div class="col-6 mb-2">
                                        <small class="text-muted">Garantie:</small><br>
                                        <span><i class="fas fa-shield-alt text-success me-1"></i>@_product.WarrantyMonths mois</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <!-- Variantes -->
                    @if (_product.Variants != null && _product.Variants.Any())
                    {
                        <div class="product-variants mb-4">
                            <h6><i class="fas fa-layer-group me-2"></i>Variantes disponibles</h6>
                            <div class="variant-buttons">
                                @foreach (var variant in _product.Variants)
                                {
                                    <button class="btn @(_selectedVariant?.Id == variant.Id ? "btn-primary" : "btn-outline-primary") me-2 mb-2" @onclick="() => SelectVariant(variant)">
                                        @variant.Name
                                        @if (variant.Price != _product.Price)
                                        {
                                            <small class="d-block">@variant.Price.ToString("N0") GNF</small>
                                        }
                                    </button>
                                }
                            </div>
                        </div>
                    }

                    <!-- Stock et commande -->
                    <div class="product-order mb-4">
                        <div class="stock-info mb-3">
                            @if (_product.Stock > 0)
                            {
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>En stock (@_product.Stock disponibles)
                                </span>
                            }
                            else
                            {
                                <span class="badge bg-danger">
                                    <i class="fas fa-times me-1"></i>Rupture de stock
                                </span>
                            }
                        </div>

                        @if (_product.Stock > 0)
                        {
                            <div class="quantity-selector mb-3">
                                <label class="form-label">Quantité:</label>
                                <div class="d-flex align-items-center">
                                    <input type="number" class="form-control me-3" style="width: 100px;"
                                           @bind="_quantity"
                                           min="@(_product.MinOrderQuantity ?? 1)"
                                           max="@(Math.Min(_product.Stock, _product.MaxOrderQuantity ?? _product.Stock))">
                                    @if (_product.MinOrderQuantity.HasValue && _product.MinOrderQuantity > 1)
                                    {
                                        <small class="text-muted">Min: @_product.MinOrderQuantity</small>
                                    }
                                </div>
                            </div>

                            <button class="btn btn-primary btn-lg w-100" @onclick="AddToCart">
                                <i class="fas fa-shopping-cart me-2"></i> Ajouter au panier
                            </button>
                        }
                    </div>

                    <!-- Tags -->
                    @if (!string.IsNullOrEmpty(_product.Tags))
                    {
                        <div class="product-tags mt-4">
                            <h6><i class="fas fa-tags me-2"></i>Tags</h6>
                            <div class="tags-container">
                                @foreach (var tag in _product.Tags.Split(',', StringSplitOptions.RemoveEmptyEntries))
                                {
                                    <span class="badge bg-light text-dark me-1 mb-1">#@tag.Trim()</span>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4">Avis clients</h3>



                <!-- Review Summary -->
                <ReviewSummary Summary="_reviewSummary"
                              ShowWriteReviewButton="@_canWriteReview"
                              IsEditMode="@(_existingUserReview != null)"
                              OnWriteReviewClick="ShowReviewForm" />

                <!-- Review Form -->
                @if (_showReviewForm)
                {
                    <div class="mb-4">
                        <ReviewForm ProductId="Id"
                                   ExistingReview="@_editingReview"
                                   IsEdit="@(_editingReview != null)"
                                   ShowVerifiedPurchase="false"
                                   OnSubmit="HandleCreateReview"
                                   OnUpdate="HandleUpdateReview"
                                   OnCancel="HideReviewForm" />
                    </div>
                }

                <!-- Reviews List -->
                <ReviewsList Reviews="_reviews"
                            ShowActions="true"
                            ShowPagination="true"
                            CurrentPage="_currentPage"
                            TotalPages="_totalPages"
                            CurrentUserId="@_currentUserId"
                            OnEditReview="EditReview"
                            OnDeleteReview="DeleteReview"
                            OnPageChanged="LoadReviews"
                            OnToggleHelpful="ToggleHelpful" />
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public int Id { get; set; }

    private ProductDto? _product;
    private ProductVariantDto? _selectedVariant;
    private int _quantity = 1;

    // Reviews related fields
    private ReviewSummaryDto? _reviewSummary;
    private List<ReviewDto> _reviews = new();
    private bool _showReviewForm = false;
    private ReviewDto? _editingReview = null;
    private bool _canWriteReview = false;
    private string? _currentUserId;
    private string? _currentUserName;
    private ReviewDto? _existingUserReview;
    private int _currentPage = 1;
    private int _totalPages = 1;
    private const int _pageSize = 10;



    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        _product = await ProductService.GetProductByIdAsync(Id);
        if (_product?.Variants?.Any() == true)
        {
            _selectedVariant = _product.Variants.First();
        }

        // Load user info
        var authState = await AuthenticationStateTask;
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            _currentUserId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            _currentUserName = authState.User.FindFirst(ClaimTypes.Name)?.Value ?? authState.User.FindFirst(ClaimTypes.Email)?.Value;

            if (!string.IsNullOrEmpty(_currentUserId))
            {
                _canWriteReview = await ReviewService.CanUserReviewProductAsync(Id, _currentUserId);

                // Si l'utilisateur ne peut pas écrire d'avis, vérifier s'il a un avis existant à modifier
                if (!_canWriteReview)
                {
                    var userReviews = await ReviewService.GetUserReviewsAsync(_currentUserId, 1, 100);
                    _existingUserReview = userReviews.Reviews.FirstOrDefault(r => r.ProductId == Id);
                    _canWriteReview = true; // Permettre la modification
                }
            }
        }

        // Load reviews data
        await LoadReviewsData();
    }

    private void SelectVariant(ProductVariantDto variant)
    {
        _selectedVariant = variant;
    }

    private async Task AddToCart()
    {
        var item = new CartItemCreateDto
        {
            ProductId = _product.Id,
            ProductName = _product.Name,
            Price = _selectedVariant?.Price ?? _product.Price,
            Quantity = _quantity,
            VariantId = _selectedVariant?.Id,
            VariantName = _selectedVariant?.Name
        };

        var authState = await AuthenticationStateTask;
        var user = authState.User;
        string userId;

        if (user.Identity.IsAuthenticated)
        {
            userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? "";
        }
        else
        {
            // Utiliser un ID de session temporaire pour les utilisateurs non connectés
            userId = await GetOrCreateGuestUserId();
        }

        await CartService.AddItemToCartAsync(userId, item);
        NavigationManager.NavigateTo("/cart");
    }

    private async Task LoadReviewsData()
    {
        try
        {
            // Load review summary
            _reviewSummary = await ReviewService.GetReviewSummaryAsync(Id);

            // Load reviews
            var reviewsResult = await ReviewService.GetReviewsByProductIdAsync(Id, _currentPage, _pageSize);
            _reviews = reviewsResult.Reviews;
            _totalPages = reviewsResult.TotalPages;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des reviews: {ex.Message}");
        }
    }

    private async Task LoadReviews(int page)
    {
        _currentPage = page;
        await LoadReviewsData();
        StateHasChanged();
    }

    private void ShowReviewForm()
    {
        if (!string.IsNullOrEmpty(_currentUserId))
        {
            _showReviewForm = true;
            _editingReview = _existingUserReview; // Pré-remplir avec l'avis existant s'il y en a un
            StateHasChanged();
        }
        else
        {
            NavigationManager.NavigateTo("/auth/login");
        }
    }

    private void HideReviewForm()
    {
        _showReviewForm = false;
        _editingReview = null;
        StateHasChanged();
    }

    private async Task HandleCreateReview(CreateReviewRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentUserId) || string.IsNullOrEmpty(_currentUserName))
            {
                NavigationManager.NavigateTo("/auth/login");
                return;
            }

            request.UserId = _currentUserId;
            request.UserName = _currentUserName;

            await ReviewService.CreateReviewAsync(request);

            HideReviewForm();
            await LoadReviewsData();
            _canWriteReview = false; // User can only write one review per product
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création de la review: {ex.Message}");
            // TODO: Show error message to user
        }
    }

    private async Task HandleUpdateReview(UpdateReviewRequest request)
    {
        try
        {
            if (_editingReview == null) return;

            await ReviewService.UpdateReviewAsync(_editingReview.Id, request);

            HideReviewForm();
            await LoadReviewsData();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour de la review: {ex.Message}");
            // TODO: Show error message to user
        }
    }

    private void EditReview(ReviewDto review)
    {
        _editingReview = review;
        _showReviewForm = true;
        StateHasChanged();
    }

    private async Task DeleteReview(ReviewDto review)
    {
        try
        {
            await ReviewService.DeleteReviewAsync(review.Id);
            await LoadReviewsData();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            // Handle error appropriately
        }
    }

    private async Task ToggleHelpful(ReviewDto review)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentUserId))
            {
                NavigationManager.NavigateTo("/auth/login");
                return;
            }

            await ReviewService.MarkReviewHelpfulAsync(review.Id);
            await LoadReviewsData();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            // Handle error appropriately
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }

    private bool HasTechnicalInfo()
    {
        return !string.IsNullOrEmpty(_product?.Color) ||
               !string.IsNullOrEmpty(_product?.Size) ||
               !string.IsNullOrEmpty(_product?.Material) ||
               !string.IsNullOrEmpty(_product?.Manufacturer) ||
               !string.IsNullOrEmpty(_product?.CountryOfOrigin) ||
               !string.IsNullOrEmpty(_product?.Condition) ||
               (_product?.WarrantyMonths.HasValue == true && _product.WarrantyMonths > 0);
    }
}
