@page "/dashboard"
@page "/"
@attribute [Authorize]
@using NafaPlace.SellerPortal.Models.Auth
@using NafaPlace.SellerPortal.Models.Orders
@using NafaPlace.SellerPortal.Models.Statistics
@using NafaPlace.SellerPortal.Services
@using NafaPlace.Catalog.Domain.Models
@using System.IdentityModel.Tokens.Jwt
@inject IAuthService AuthService
@inject IOrderService OrderService
@inject IStatisticsService StatisticsService
@inject NotificationService NotificationService
@inject ProductService ProductService
@inject IJSRuntime JSRuntime

<PageTitle>Dashboard Vendeur - NafaPlace</PageTitle>

<style>
    .stats-card {
        border-radius: 12px;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
        background: white;
        padding: 20px;
        margin-bottom: 20px;
    }
    .stats-card:hover {
        transform: translateY(-2px);
    }
    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        margin-right: 16px;
    }
    .stats-value {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        color: #2c3e50;
    }
    .stats-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
        margin-bottom: 8px;
    }
    .stats-change {
        font-size: 0.8rem;
        font-weight: 600;
        color: #27ae60;
    }
    .chart-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 24px;
        margin-bottom: 24px;
    }
    .chart-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #e74c3c;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }
    .chart-title i {
        margin-right: 8px;
    }
</style>

<div class="container-fluid px-4">
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon" style="background-color: #e74c3c;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="stats-label">Visiteurs Uniques</p>
                    <h3 class="stats-value">12,5K</h3>
                    <p class="stats-change mb-0">
                        <i class="fas fa-arrow-up"></i> +12,5% vs période précédente
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon" style="background-color: #27ae60;">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="stats-label">Commandes</p>
                    <h3 class="stats-value">1,2K</h3>
                    <p class="stats-change mb-0">
                        <i class="fas fa-arrow-up"></i> +8,3% vs période précédente
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon" style="background-color: #f39c12;">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="stats-label">Chiffre d'Affaires</p>
                    <h3 class="stats-value">15,7M GNF</h3>
                    <p class="stats-change mb-0">
                        <i class="fas fa-arrow-up"></i> +16,7% vs période précédente
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon" style="background-color: #3498db;">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="stats-label">Taux de Conversion</p>
                    <h3 class="stats-value">4,2%</h3>
                    <p class="stats-change mb-0">
                        <i class="fas fa-arrow-up"></i> +2,1% vs période précédente
                    </p>
                </div>
            </div>
        </div>
    </div>
    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-danger"></i>
                        Évolution des Ventes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Mois</th>
                                    <th>Ventes</th>
                                    <th>Progression</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Jan</strong></td>
                                    <td>8K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-danger" style="width: 40%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Fév</strong></td>
                                    <td>12K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-warning" style="width: 63%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Mar</strong></td>
                                    <td>10K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-info" style="width: 50%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Avr</strong></td>
                                    <td>16K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" style="width: 83%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Mai</strong></td>
                                    <td>14K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" style="width: 73%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Jun</strong></td>
                                    <td>19K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-dark" style="width: 100%"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie text-danger"></i>
                        Top Catégories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="category-list">
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-tshirt text-danger"></i> Mode</span>
                                <strong>40%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-danger" style="width: 40%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-laptop text-warning"></i> Électronique</span>
                                <strong>30%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-warning" style="width: 30%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-home text-success"></i> Maison</span>
                                <strong>20%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: 20%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-football-ball text-info"></i> Sport</span>
                                <strong>10%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: 10%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Performance Table -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="chart-title mb-3">Performance des Produits</h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Vues</th>
                                <th>Commandes</th>
                                <th>Revenus</th>
                                <th>Taux de Conversion</th>
                                <th>Tendance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.FirstOrDefault())" alt="Sac noir" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Sac noir</strong><br>
                                            <small class="text-muted">ID: 1</small>
                                        </div>
                                    </div>
                                </td>
                                <td>680</td>
                                <td>33</td>
                                <td>347 843 GNF</td>
                                <td>2,5%</td>
                                <td><span class="badge bg-success">↗ +10%</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.Skip(1).FirstOrDefault())" alt="Voiture" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Voiture</strong><br>
                                            <small class="text-muted">ID: 2</small>
                                        </div>
                                    </div>
                                </td>
                                <td>597</td>
                                <td>12</td>
                                <td>172 459 GNF</td>
                                <td>6,7%</td>
                                <td><span class="badge bg-success">↗ +10%</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/images/products/robe-mariage.jpg" alt="Robe de mariage" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Robe de mariage</strong><br>
                                            <small class="text-muted">ID: 3</small>
                                        </div>
                                    </div>
                                </td>
                                <td>608</td>
                                <td>49</td>
                                <td>433 495 GNF</td>
                                <td>0,6%</td>
                                <td><span class="badge bg-success">↗ +18%</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.Skip(2).FirstOrDefault())" alt="Costume" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Costume</strong><br>
                                            <small class="text-muted">ID: 4</small>
                                        </div>
                                    </div>
                                </td>
                                <td>402</td>
                                <td>11</td>
                                <td>157 356 GNF</td>
                                <td>3,5%</td>
                                <td><span class="badge bg-success">↗ +17%</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.Skip(3).FirstOrDefault())" alt="Basket Nike" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Basket Nike</strong><br>
                                            <small class="text-muted">ID: 5</small>
                                        </div>
                                    </div>
                                </td>
                                <td>583</td>
                                <td>22</td>
                                <td>491 712 GNF</td>
                                <td>1,0%</td>
                                <td><span class="badge bg-success">↗ +27%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool _isLoading = false;
    private UserDto? _currentUser;
    private int _productCount = 0;
    private int _pendingOrdersCount = 0;
    private decimal _monthlySales = 0;
    private int _outOfStockCount = 0;
    private List<OrderSummary> _recentOrders = new List<OrderSummary>();
    private int _sellerId = 0; // ID du vendeur connecté
    private List<Product> _topProducts = new List<Product>();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _isLoading = true;

            // Récupérer les informations de l'utilisateur connecté
            _currentUser = await AuthService.GetCurrentUserAsync();

            // Récupérer l'ID du vendeur connecté
            await LoadSellerInfo();

            // Charger les vraies données du dashboard seulement si on a un vendeur valide
            if (_sellerId > 0)
            {
                await LoadDashboardDataAsync();
            }
        }
        catch (Exception ex)
        {
            NotificationService.ShowError($"Erreur lors du chargement des données: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    // Plus besoin d'initialiser les graphiques Chart.js

    private async Task LoadDashboardDataAsync()
    {
        try
        {
            // Charger les statistiques du dashboard pour ce vendeur
            var statsRequest = new StatisticsRequest
            {
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now,
                SellerId = _sellerId // Filtrer par vendeur
            };

            var dashboardStats = await StatisticsService.GetDashboardStatisticsAsync(statsRequest);

            if (dashboardStats?.SalesStats != null)
            {
                _monthlySales = (decimal)dashboardStats.SalesStats.TotalSales;
                _pendingOrdersCount = dashboardStats.SalesStats.TotalOrders;
            }

            // Charger les commandes récentes pour ce vendeur
            var ordersRequest = new OrderFilterRequest
            {
                PageNumber = 1,
                PageSize = 5,
                SortBy = "OrderDate",
                SortDirection = "desc",
                SellerId = _sellerId // Filtrer par vendeur
            };

            var ordersResponse = await OrderService.GetOrdersAsync(ordersRequest);

            if (ordersResponse?.Orders != null)
            {
                _recentOrders = ordersResponse.Orders.Select(o => new OrderSummary
                {
                    Id = o.OrderNumber,
                    CustomerName = o.CustomerName,
                    OrderDate = o.OrderDate,
                    TotalAmount = o.TotalAmount,
                    Status = o.Status
                }).ToList();

                // Compter les commandes en attente
                _pendingOrdersCount = ordersResponse.Orders.Count(o => o.Status == "En attente" || o.Status == "Pending");
            }

            // Récupérer le nombre de produits du vendeur
            if (_sellerId > 0)
            {
                _productCount = await ProductService.GetProductsCountAsync(_sellerId);

                // Charger les top produits pour l'affichage
                var productsResponse = await ProductService.GetProductsAsync(_sellerId, 1, 5);
                _topProducts = productsResponse?.Products?.Take(5).ToList() ?? new List<Product>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des données du dashboard: {ex.Message}");
            // En cas d'erreur, garder les valeurs par défaut (0)
        }
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "En attente" => "bg-warning",
            "Expédié" => "bg-info",
            "Livré" => "bg-success",
            "Annulé" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    public class OrderSummary
    {
        public string Id { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; } = DateTime.Now;
        public decimal TotalAmount { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    private async Task LoadSellerInfo()
    {
        try
        {
            _sellerId = await GetCurrentSellerIdAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des informations du vendeur: {ex.Message}");
        }
    }

    private async Task<int> GetCurrentSellerIdAsync()
    {
        try
        {
            var token = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (string.IsNullOrEmpty(token))
                return 0;

            token = token.Trim('"');
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);

            var sellerIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "SellerId" || x.Type == "sellerId");
            if (sellerIdClaim != null && int.TryParse(sellerIdClaim.Value, out int sellerId))
            {
                return sellerId;
            }

            return 0;
        }
        catch (Exception)
        {
            return 0;
        }
    }

    private string GetProductImageSrc(Product? product)
    {
        try
        {
            if (product?.Images != null && product.Images.Any())
            {
                var mainImage = product.Images.FirstOrDefault(i => i.IsMain);
                if (mainImage != null)
                {
                    return ProductService.GetImageUrl(mainImage, true);
                }

                var firstImage = product.Images.FirstOrDefault();
                if (firstImage != null)
                {
                    return ProductService.GetImageUrl(firstImage, true);
                }
            }

            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
        catch (Exception)
        {
            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
    }

    // Plus besoin de flag pour les graphiques

    // Plus besoin d'initialiser Chart.js
}
