@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Services
@using NafaPlace.Web.Components.Reviews
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IProductService ProductService
@inject ICartService CartService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="product-grid @CssClass">
    @if (!string.IsNullOrEmpty(Title))
    {
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="section-title">
                    @if (!string.IsNullOrEmpty(Icon))
                    {
                        <i class="@Icon me-2"></i>
                    }
                    @Title
                </h2>
                @if (!string.IsNullOrEmpty(Subtitle))
                {
                    <p class="section-subtitle text-muted mb-0">@Subtitle</p>
                }
            </div>
            @if (!string.IsNullOrEmpty(ViewAllUrl))
            {
                <a href="@ViewAllUrl" class="btn btn-outline-primary">
                    Voir tout <i class="bi bi-arrow-right ms-1"></i>
                </a>
            }
        </div>
    }

    @if (isLoading)
    {
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else if (displayedProducts == null || !displayedProducts.Any())
    {
        <div class="empty-state text-center py-4">
            <i class="bi bi-box-seam display-4 text-muted"></i>
            <p class="text-muted mt-2">@EmptyMessage</p>
        </div>
    }
    else
    {
        <div class="row g-3 g-md-4">
            @foreach (var product in displayedProducts)
            {
                <div class="col-6 col-md-4 col-lg-3">
                    <div class="card product-card h-100">
                        @if (ShowBadges)
                        {
                            @if (SectionType == "new")
                            {
                                <span class="badge-new">NOUVEAU</span>
                            }
                            else if (SectionType == "featured")
                            {
                                <span class="badge-featured">VEDETTE</span>
                            }
                            
                            @if (product.DiscountPercentage > 0)
                            {
                                <span class="badge badge-sale">-@product.DiscountPercentage%</span>
                            }
                        }

                        <a href="/catalog/products/@product.Id">
                            <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")" 
                                 class="card-img-top" alt="@product.Name" loading="lazy">
                        </a>

                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">
                                <a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a>
                            </h6>
                            
                            @if (ShowRatings)
                            {
                                <div class="d-flex mb-2">
                                    <StarRating Rating="product.Rating" ShowRatingText="false" CssClass="me-2 small" />
                                    <small class="text-muted">(@product.ReviewCount)</small>
                                </div>
                            }
                            
                            @if (ShowCategory)
                            {
                                <p class="card-text text-muted small">@product.Category?.Name</p>
                            }
                            
                            <div class="price-section mt-auto mb-2">
                                @if (product.DiscountPercentage > 0 && product.OldPrice.HasValue)
                                {
                                    <div class="price-with-discount">
                                        <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                        <span class="original-price">@product.OldPrice.Value.ToString("N0") GNF</span>
                                    </div>
                                }
                                else
                                {
                                    <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                }
                            </div>

                            <div class="card-actions">
                                <button class="btn btn-primary btn-sm w-100" @onclick="() => AddToCart(product.Id)" disabled="@(product.Stock <= 0)">
                                    <i class="bi bi-cart-plus me-1"></i>
                                    @if (product.Stock <= 0)
                                    {
                                        <span>Rupture</span>
                                    }
                                    else
                                    {
                                        <span>Ajouter</span>
                                    }
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        @if (ShowLoadMore && allProducts != null && allProducts.Count() > displayedProducts.Count())
        {
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" @onclick="LoadMore">
                    <i class="bi bi-plus-circle me-2"></i>
                    Voir plus (@(allProducts.Count() - displayedProducts.Count()) produits restants)
                </button>
            </div>
        }
    }
</div>

@code {
    [Parameter] public string Title { get; set; } = "";
    [Parameter] public string Subtitle { get; set; } = "";
    [Parameter] public string Icon { get; set; } = "";
    [Parameter] public string SectionType { get; set; } = "featured"; // "featured", "new", "bestseller"
    [Parameter] public string ViewAllUrl { get; set; } = "";
    [Parameter] public string EmptyMessage { get; set; } = "Aucun produit disponible pour le moment.";
    [Parameter] public string CssClass { get; set; } = "";
    [Parameter] public bool ShowBadges { get; set; } = true;
    [Parameter] public bool ShowRatings { get; set; } = true;
    [Parameter] public bool ShowCategory { get; set; } = false;
    [Parameter] public bool ShowLoadMore { get; set; } = true;
    [Parameter] public int InitialItemsToShow { get; set; } = 8;
    [Parameter] public int ItemsToLoadMore { get; set; } = 4;
    [Parameter] public int MaxItemsToLoad { get; set; } = 20;
    [Parameter] public int? CategoryId { get; set; }
    [Parameter] public string SortBy { get; set; } = "newest";

    private IEnumerable<ProductDto>? allProducts;
    private IEnumerable<ProductDto>? displayedProducts;
    private bool isLoading = true;
    private int currentDisplayCount;
    private string? _userId;

    protected override async Task OnInitializedAsync()
    {
        // Récupérer l'utilisateur connecté
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        }

        currentDisplayCount = InitialItemsToShow;
        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            switch (SectionType.ToLower())
            {
                case "new":
                    allProducts = await ProductService.GetNewProductsAsync(MaxItemsToLoad);
                    break;
                case "featured":
                default:
                    allProducts = await ProductService.GetFeaturedProductsAsync(MaxItemsToLoad);
                    break;
            }

            UpdateDisplayedProducts();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading products: {ex.Message}");
            allProducts = new List<ProductDto>();
            displayedProducts = new List<ProductDto>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void UpdateDisplayedProducts()
    {
        if (allProducts != null)
        {
            displayedProducts = allProducts.Take(currentDisplayCount);
        }
    }

    private void LoadMore()
    {
        currentDisplayCount = Math.Min(currentDisplayCount + ItemsToLoadMore, MaxItemsToLoad);
        UpdateDisplayedProducts();
        StateHasChanged();
    }

    private async Task AddToCart(int productId)
    {
        string userId;
        if (string.IsNullOrEmpty(_userId))
        {
            userId = await GetOrCreateGuestUserId();
        }
        else
        {
            userId = _userId;
        }

        try
        {
            var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };
            var result = await CartService.AddItemToCartAsync(userId, cartItem);
            await JSRuntime.InvokeVoidAsync("showToast", $"✅ Produit ajouté au panier !", "success");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"❌ Erreur: {ex.Message}", "error");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }
        return guestId;
    }
}
