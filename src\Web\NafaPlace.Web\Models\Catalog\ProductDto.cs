namespace NafaPlace.Web.Models.Catalog;

public class ProductDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ShortDescription { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public decimal? OldPrice { get; set; }
    public int DiscountPercentage { get; set; }
    public string Currency { get; set; } = "GNF";
    public int Stock { get; set; }
    public double Rating { get; set; }
    public int ReviewCount { get; set; }
    public string Brand { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string Sku { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public string Material { get; set; } = string.Empty;
    public string Manufacturer { get; set; } = string.Empty;
    public string CountryOfOrigin { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public int? WarrantyMonths { get; set; }
    public string Features { get; set; } = string.Empty;
    public string Tags { get; set; } = string.Empty;
    public decimal? CompareAtPrice { get; set; }
    public DateTime? AvailableFrom { get; set; }
    public int? MinOrderQuantity { get; set; }
    public int? MaxOrderQuantity { get; set; }
    public bool RequiresShipping { get; set; }
    public bool IsDigital { get; set; }
    public int CategoryId { get; set; }
    public CategoryDto? Category { get; set; }
    public List<ProductImageDto> Images { get; set; } = new List<ProductImageDto>();
    public string MainImageUrl => Images.FirstOrDefault(i => i.IsMain)?.Url ?? Images.FirstOrDefault()?.Url ?? string.Empty;
    public List<ProductAttributeDto> Attributes { get; set; } = new List<ProductAttributeDto>();
    public List<ProductVariantDto> Variants { get; set; } = new List<ProductVariantDto>();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Propriétés calculées
    public bool HasDiscount => OldPrice.HasValue && OldPrice > Price;
    public string CategoryName => Category?.Name ?? string.Empty;
    public int CurrentStock => Stock;
}
