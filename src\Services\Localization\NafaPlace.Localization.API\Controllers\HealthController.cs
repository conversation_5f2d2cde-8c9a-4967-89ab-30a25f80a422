using Microsoft.AspNetCore.Mvc;

namespace NafaPlace.Localization.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(new { Status = "Healthy", Service = "Localization API", Timestamp = DateTime.UtcNow });
    }
}

[ApiController]
[Route("api/[controller]")]
public class LocalizationController : ControllerBase
{
    [HttpGet("languages")]
    public IActionResult GetLanguages()
    {
        return Ok(new[] { "fr", "en", "sus", "pul" });
    }

    [HttpGet("translations/{language}")]
    public IActionResult GetTranslations(string language)
    {
        return Ok(new { Language = language, Message = "Translations - Coming Soon" });
    }
}
