#!/usr/bin/env pwsh

Write-Host "=== BUILD ET DÉMARRAGE PROGRESSIF ===" -ForegroundColor Green

# Arrêter tous les services
Write-Host "1. Arrêt de tous les services..." -ForegroundColor Yellow
docker-compose down

# Démarrer les bases de données
Write-Host "2. Démarrage des bases de données..." -ForegroundColor Yellow
docker-compose up -d `
    catalog-db `
    identity-db `
    order-db `
    reviews-db `
    notifications-db `
    wishlist-db `
    inventory-db `
    coupon-db `
    delivery-db `
    redis `
    azurite `
    pgadmin

Write-Host "3. Attente des bases de données (30s)..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Démarrer les services principaux qui fonctionnent
Write-Host "4. Build et démarrage des services principaux..." -ForegroundColor Yellow
docker-compose up --build -d `
    identity-api `
    catalog-api `
    cart-api `
    order-api `
    payment-api `
    reviews-api `
    notifications-api `
    wishlist-api `
    inventory-api `
    coupon-api `
    delivery-api

Write-Host "5. Attente des services principaux (20s)..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# Démarrer l'API Gateway
Write-Host "6. Démarrage de l'API Gateway..." -ForegroundColor Yellow
docker-compose up --build -d api-gateway

Start-Sleep -Seconds 10

# Démarrer les portails web
Write-Host "7. Démarrage des portails web..." -ForegroundColor Yellow
docker-compose up --build -d web admin-portal seller-portal

Write-Host "8. Attente finale (15s)..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Vérifier l'état
Write-Host "=== ÉTAT DES SERVICES ===" -ForegroundColor Green
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Sort-Object

Write-Host ""
Write-Host "=== SERVICES DISPONIBLES ===" -ForegroundColor Green
Write-Host "Web Portal:     http://localhost:8080" -ForegroundColor Cyan
Write-Host "Admin Portal:   http://localhost:8081" -ForegroundColor Cyan
Write-Host "Seller Portal:  http://localhost:8082" -ForegroundColor Cyan
Write-Host "API Gateway:    http://localhost:5000" -ForegroundColor Cyan

Write-Host ""
Write-Host "=== TEST DE CONNECTIVITÉ ===" -ForegroundColor Yellow

$services = @(
    @{Name="Web Portal"; Url="http://localhost:8080"},
    @{Name="API Gateway"; Url="http://localhost:5000/health"},
    @{Name="Identity API"; Url="http://localhost:5155/health"},
    @{Name="Catalog API"; Url="http://localhost:5243/health"}
)

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.Url -Method GET -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name) - OK" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $($service.Name) - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($service.Name) - FAILED" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 SERVICES PRINCIPAUX DÉMARRÉS !" -ForegroundColor Green
Write-Host "Les nouveaux services seront ajoutés progressivement" -ForegroundColor Yellow
