# Documentation de la Pagination - NafaPlace

## Vue d'ensemble

Le système de pagination de NafaPlace comprend plusieurs composants réutilisables qui permettent de gérer l'affichage paginé des données de manière cohérente et accessible dans toute l'application.

## Composants Disponibles

### 1. Composant `Pagination`

Le composant de base pour afficher une pagination simple.

**Localisation :** `src/Web/NafaPlace.Web/Shared/Components/Pagination.razor`

**Utilisation :**
```razor
<Pagination CurrentPage="@currentPage" 
            TotalPages="@totalPages" 
            TotalItems="@totalItems"
            MaxVisiblePages="5"
            ShowInfo="true"
            ShowIcons="true"
            OnPageChange="OnPageChanged" />
```

**Paramètres :**
- `CurrentPage` (int) : Page courante (défaut: 1)
- `TotalPages` (int) : Nombre total de pages (défaut: 1)
- `TotalItems` (int) : Nombre total d'éléments (défaut: 0)
- `MaxVisiblePages` (int) : Nombre maximum de pages visibles (défaut: 5)
- `ShowInfo` (bool) : Afficher les informations de pagination (défaut: true)
- `ShowIcons` (bool) : Utiliser des icônes pour les boutons précédent/suivant (défaut: true)
- `CssClass` (string) : Classes CSS personnalisées
- `PaginationClass` (string) : Classes CSS pour la pagination Bootstrap (défaut: "justify-content-center")
- `AriaLabel` (string) : Label d'accessibilité (défaut: "Navigation des pages")
- `OnPageChange` (EventCallback<int>) : Callback appelé lors du changement de page

### 2. Composant `PaginationControls`

Composant complet avec contrôles de taille de page et informations détaillées.

**Localisation :** `src/Web/NafaPlace.Web/Shared/Components/PaginationControls.razor`

**Utilisation :**
```razor
<PaginationControls CurrentPage="@currentPage" 
                    TotalPages="@totalPages" 
                    TotalItems="@totalItems"
                    PageSize="@pageSize"
                    PageSizeOptions="@(new[] { 10, 20, 50, 100 })"
                    ShowPageSizeSelector="true"
                    OnPageChange="OnPageChanged"
                    OnPageSizeChange="OnPageSizeChanged" />
```

**Paramètres supplémentaires :**
- `PageSize` (int) : Taille de page courante (défaut: 10)
- `PageSizeOptions` (int[]) : Options de taille de page disponibles
- `ShowPageSizeSelector` (bool) : Afficher le sélecteur de taille de page (défaut: true)
- `OnPageSizeChange` (EventCallback<int>) : Callback appelé lors du changement de taille de page

### 3. Composant `ProductSection`

Composant spécialisé pour afficher des sections de produits avec pagination intégrée.

**Localisation :** `src/Web/NafaPlace.Web/Shared/Components/ProductSection.razor`

**Utilisation :**
```razor
<ProductSection Title="Nouveautés" 
                SectionType="new"
                ViewAllUrl="/nouveautes"
                ColumnClass="col-6 col-md-3"
                InitialPageSize="8"
                PageSizeOptions="@(new[] { 8, 16, 24, 32 })"
                ShowPagination="true"
                ShowQuickActions="true" />
```

**Paramètres spécialisés :**
- `Title` (string) : Titre de la section
- `SectionType` (string) : Type de section ("featured", "new", "bestseller")
- `ViewAllUrl` (string) : URL pour voir tous les produits
- `ColumnClass` (string) : Classes CSS pour les colonnes (défaut: "col-6 col-md-3")
- `ShowBadges` (bool) : Afficher les badges sur les produits (défaut: true)
- `ShowRatings` (bool) : Afficher les évaluations (défaut: true)
- `ShowCategory` (bool) : Afficher la catégorie (défaut: true)
- `ShowQuickActions` (bool) : Afficher les actions rapides (défaut: false)
- `CategoryId` (int?) : Filtrer par catégorie
- `SortBy` (string) : Critère de tri (défaut: "newest")

## Services de Pagination

### Méthodes de Service Paginées

Le `ProductService` inclut des méthodes spécialement conçues pour la pagination :

```csharp
// Nouveaux produits avec pagination
Task<PagedResultDto<ProductDto>> GetNewProductsPaginatedAsync(
    int page = 1, 
    int pageSize = 12, 
    int? categoryId = null, 
    string sortBy = "newest"
);

// Produits en vedette avec pagination
Task<PagedResultDto<ProductDto>> GetFeaturedProductsPaginatedAsync(
    int page = 1, 
    int pageSize = 12, 
    int? categoryId = null, 
    string sortBy = "newest"
);
```

### Modèle `PagedResultDto<T>`

```csharp
public class PagedResultDto<T>
{
    public IEnumerable<T> Items { get; set; }
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}
```

## Styles CSS

Les styles de pagination sont définis dans `src/Web/NafaPlace.Web/wwwroot/css/pagination.css` et incluent :

- Styles responsive pour mobile et desktop
- Animations et transitions
- Thème cohérent avec les couleurs de NafaPlace
- Support de l'accessibilité
- Styles pour les badges de produits

## Exemples d'Implémentation

### Page avec Pagination Simple

```razor
@code {
    private int currentPage = 1;
    private int pageSize = 12;
    private PagedResultDto<ProductDto>? pagedResult;

    protected override async Task OnInitializedAsync()
    {
        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        pagedResult = await ProductService.GetNewProductsPaginatedAsync(
            currentPage, pageSize);
    }

    private async Task OnPageChanged(int page)
    {
        currentPage = page;
        await LoadProducts();
    }

    private async Task OnPageSizeChanged(int newPageSize)
    {
        pageSize = newPageSize;
        currentPage = 1;
        await LoadProducts();
    }
}
```

### Intégration dans une Page Existante

```razor
<!-- Affichage des produits -->
<div class="row g-4">
    @if (pagedResult?.Items != null)
    {
        @foreach (var product in pagedResult.Items)
        {
            <!-- Carte produit -->
        }
    }
</div>

<!-- Pagination -->
<PaginationControls CurrentPage="@currentPage" 
                    TotalPages="@(pagedResult?.TotalPages ?? 1)" 
                    TotalItems="@(pagedResult?.TotalCount ?? 0)"
                    PageSize="@pageSize"
                    OnPageChange="OnPageChanged"
                    OnPageSizeChange="OnPageSizeChanged" />
```

## Bonnes Pratiques

1. **Performance** : Utilisez toujours la pagination côté serveur pour les grandes collections
2. **UX** : Affichez des indicateurs de chargement pendant les transitions
3. **Accessibilité** : Utilisez les labels ARIA appropriés
4. **Responsive** : Testez sur différentes tailles d'écran
5. **SEO** : Considérez l'utilisation de paramètres d'URL pour la pagination

## Test et Débogage

Une page de test complète est disponible à `/test/pagination` pour tester tous les composants de pagination avec différentes configurations.

## Pages Utilisant la Pagination

- `/catalog` - Catalogue de produits avec filtres
- `/nouveautes` - Nouveaux produits
- `/meilleures-ventes` - Meilleures ventes
- Page d'accueil - Sections de produits (optionnel)

## Configuration API

Les endpoints API supportent les paramètres de pagination standard :
- `page` : Numéro de page (commence à 1)
- `pageSize` : Nombre d'éléments par page
- `sortBy` : Critère de tri
- `sortDescending` : Ordre de tri (true/false)
