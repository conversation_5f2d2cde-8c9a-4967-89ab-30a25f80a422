@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Services
@using NafaPlace.Web.Components.Reviews
@inject IProductService ProductService

<div class="card product-card h-100">
    @if (ShowBadges)
    {
        @if (Product.CreatedAt >= DateTime.Now.AddDays(-7))
        {
            <span class="badge-new">NOUVEAU</span>
        }
        
        @if (Product.DiscountPercentage > 0)
        {
            <span class="badge-sale">-@Product.DiscountPercentage%</span>
        }
    }

    <div class="product-image-container">
        <a href="/catalog/products/@Product.Id" class="product-link">
            <img src="@GetProductImage()" 
                 class="card-img-top" 
                 alt="@Product.Name" 
                 loading="lazy"
                 onerror="this.src='/images/placeholder.png'">
        </a>
        
        @if (Product.Stock <= 0)
        {
            <div class="stock-overlay">
                <span class="stock-badge">Rupture de stock</span>
            </div>
        }
    </div>

    <div class="card-body">
        <h6 class="card-title">
            <a href="/catalog/products/@Product.Id" class="product-title-link">
                @Product.Name
            </a>
        </h6>
        
        @if (ShowRatings && Product.ReviewCount > 0)
        {
            <div class="product-rating mb-2">
                <StarRating Rating="Product.Rating" ShowRatingText="false" CssClass="rating-stars" />
                <small class="rating-count">(@Product.ReviewCount)</small>
            </div>
        }
        
        @if (ShowCategory && Product.Category != null)
        {
            <p class="product-category">@Product.Category.Name</p>
        }
        
        <div class="price-section">
            @if (Product.DiscountPercentage > 0 && Product.OldPrice.HasValue)
            {
                <div class="price-with-discount">
                    <span class="current-price">@Product.Price.ToString("N0") GNF</span>
                    <span class="original-price">@Product.OldPrice.Value.ToString("N0") GNF</span>
                </div>
                <div class="savings">
                    Économisez @((Product.OldPrice.Value - Product.Price).ToString("N0")) GNF
                </div>
            }
            else
            {
                <span class="current-price">@Product.Price.ToString("N0") GNF</span>
            }
        </div>

        <div class="card-actions">
            @if (ShowQuickActions)
            {
                <div class="d-flex gap-2">
                    <a href="/catalog/products/@Product.Id" class="btn btn-outline-primary btn-sm flex-fill">
                        <i class="bi bi-eye me-1"></i>Détails
                    </a>
                    <button class="btn btn-primary btn-sm flex-fill" 
                            @onclick="OnAddToCart" 
                            disabled="@(Product.Stock <= 0)">
                        <i class="bi bi-cart-plus me-1"></i>
                        @if (Product.Stock <= 0)
                        {
                            <span>Rupture</span>
                        }
                        else
                        {
                            <span>Ajouter</span>
                        }
                    </button>
                </div>
            }
            else
            {
                <button class="btn btn-primary w-100" 
                        @onclick="OnAddToCart" 
                        disabled="@(Product.Stock <= 0)">
                    <i class="bi bi-cart-plus me-1"></i>
                    @if (Product.Stock <= 0)
                    {
                        <span>Produit indisponible</span>
                    }
                    else
                    {
                        <span>Ajouter au panier</span>
                    }
                </button>
            }
        </div>
    </div>
</div>



@code {
    [Parameter] public ProductDto Product { get; set; } = null!;
    [Parameter] public bool ShowBadges { get; set; } = true;
    [Parameter] public bool ShowRatings { get; set; } = true;
    [Parameter] public bool ShowCategory { get; set; } = false;
    [Parameter] public bool ShowQuickActions { get; set; } = false;
    [Parameter] public EventCallback<int> OnAddToCartClick { get; set; }

    private string GetProductImage()
    {
        if (Product.Images != null && Product.Images.Any())
        {
            return ProductService.GetImageUrl(Product.Images.First());
        }
        return "/images/placeholder.png";
    }

    private async Task OnAddToCart()
    {
        if (OnAddToCartClick.HasDelegate)
        {
            await OnAddToCartClick.InvokeAsync(Product.Id);
        }
    }
}
