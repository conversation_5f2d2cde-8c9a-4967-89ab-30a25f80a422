using NafaPlace.Chat.Application.DTOs;

namespace NafaPlace.Chat.Application.Interfaces;

public interface IFAQRepository
{
    Task<List<FAQItemDto>> GetAllAsync();
    Task<FAQItemDto?> GetByIdAsync(int id);
    Task<List<FAQItemDto>> SearchAsync(string query);
    Task<List<FAQItemDto>> GetByCategoryAsync(string category);
    Task<FAQItemDto> CreateAsync(FAQItemDto faq);
    Task<FAQItemDto> UpdateAsync(FAQItemDto faq);
    Task<bool> DeleteAsync(int id);
    Task<List<string>> GetCategoriesAsync();
    Task<List<FAQItemDto>> GetPopularAsync(int count = 10);
    Task<bool> IncrementViewCountAsync(int id);
}
