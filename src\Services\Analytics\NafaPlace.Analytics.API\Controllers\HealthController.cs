using Microsoft.AspNetCore.Mvc;

namespace NafaPlace.Analytics.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(new { Status = "Healthy", Service = "Analytics API", Timestamp = DateTime.UtcNow });
    }
}

[ApiController]
[Route("api/[controller]")]
public class AnalyticsController : ControllerBase
{
    [HttpGet("dashboard")]
    public IActionResult GetDashboard()
    {
        return Ok(new { Message = "Analytics Dashboard - Coming Soon" });
    }

    [HttpGet("reports")]
    public IActionResult GetReports()
    {
        return Ok(new { Message = "Analytics Reports - Coming Soon" });
    }
}
