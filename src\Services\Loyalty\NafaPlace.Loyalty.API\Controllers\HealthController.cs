using Microsoft.AspNetCore.Mvc;

namespace NafaPlace.Loyalty.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(new { Status = "Healthy", Service = "Loyalty API", Timestamp = DateTime.UtcNow });
    }
}

[ApiController]
[Route("api/[controller]")]
public class LoyaltyController : ControllerBase
{
    [HttpGet("points/{userId}")]
    public IActionResult GetPoints(string userId)
    {
        return Ok(new { UserId = userId, Points = 1000, Level = "Gold" });
    }

    [HttpGet("rewards")]
    public IActionResult GetRewards()
    {
        return Ok(new { Message = "Loyalty Rewards - Coming Soon" });
    }
}
