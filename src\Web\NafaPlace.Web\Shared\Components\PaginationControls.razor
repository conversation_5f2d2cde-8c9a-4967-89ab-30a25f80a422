@using Microsoft.AspNetCore.Components

<div class="pagination-controls d-flex flex-column flex-md-row justify-content-between align-items-center @CssClass">
    <!-- Informations de pagination -->
    <div class="pagination-info mb-2 mb-md-0">
        @if (TotalItems > 0)
        {
            var startItem = (CurrentPage - 1) * PageSize + 1;
            var endItem = Math.Min(CurrentPage * PageSize, TotalItems);
            
            <span class="text-muted">
                Affichage de <strong>@startItem</strong> à <strong>@endItem</strong> 
                sur <strong>@TotalItems</strong> résultat@(TotalItems > 1 ? "s" : "")
            </span>
        }
        else
        {
            <span class="text-muted">Aucun résultat trouvé</span>
        }
    </div>

    <!-- Contrôles de taille de page -->
    @if (ShowPageSizeSelector && TotalItems > 0)
    {
        <div class="page-size-selector d-flex align-items-center">
            <label for="pageSize" class="form-label me-2 mb-0">Afficher :</label>
            <select id="pageSize" class="form-select form-select-sm" style="width: auto;" 
                    value="@PageSize" @onchange="OnPageSizeChanged">
                @foreach (var size in PageSizeOptions)
                {
                    <option value="@size">@size</option>
                }
            </select>
            <span class="ms-2 text-muted">par page</span>
        </div>
    }
</div>

@if (TotalPages > 1)
{
    <Pagination CurrentPage="@CurrentPage" 
                TotalPages="@TotalPages" 
                TotalItems="@TotalItems"
                MaxVisiblePages="@MaxVisiblePages"
                ShowInfo="false"
                ShowIcons="@ShowIcons"
                CssClass="@PaginationCssClass"
                PaginationClass="@PaginationClass"
                AriaLabel="@AriaLabel"
                OnPageChange="OnPageChange" />
}

@code {
    [Parameter] public int CurrentPage { get; set; } = 1;
    [Parameter] public int TotalPages { get; set; } = 1;
    [Parameter] public int TotalItems { get; set; } = 0;
    [Parameter] public int PageSize { get; set; } = 10;
    [Parameter] public int MaxVisiblePages { get; set; } = 5;
    [Parameter] public bool ShowPageSizeSelector { get; set; } = true;
    [Parameter] public bool ShowIcons { get; set; } = true;
    [Parameter] public int[] PageSizeOptions { get; set; } = new[] { 10, 20, 50, 100 };
    [Parameter] public string CssClass { get; set; } = "mt-4";
    [Parameter] public string PaginationCssClass { get; set; } = "mt-3";
    [Parameter] public string PaginationClass { get; set; } = "justify-content-center";
    [Parameter] public string AriaLabel { get; set; } = "Navigation des pages";
    [Parameter] public EventCallback<int> OnPageChange { get; set; }
    [Parameter] public EventCallback<int> OnPageSizeChange { get; set; }

    private async Task OnPageSizeChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var newPageSize))
        {
            await OnPageSizeChange.InvokeAsync(newPageSize);
        }
    }
}
