@echo off
echo === FORCER LA MIGRATION DU SERVICE CATALOG ===

echo 1. Arret du service catalog...
docker-compose stop catalog-api

echo 2. Suppression du conteneur catalog...
docker-compose rm -f catalog-api

echo 3. Rebuild et redemarrage du service catalog...
docker-compose up --build -d catalog-api

echo 4. Attente du service (30 secondes)...
timeout /t 30 /nobreak

echo 5. Test de l'API...
curl -s http://localhost:5000/api/catalog/products | findstr "id"

echo.
echo === MIGRATION TERMINEE ===
echo Testez maintenant la creation de produits !
pause
