using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace NafaPlace.Catalog.Application.DTOs.Product;

public class CreateProductRequest
{
    [Required]
    [StringLength(100)]
    public required string Name { get; set; }

    [Required]
    [StringLength(500)]
    public required string Description { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal Price { get; set; }

    [Required]
    [StringLength(3)]
    public required string Currency { get; set; } = "GNF";

    [Range(0, int.MaxValue)]
    public int StockQuantity { get; set; }

    [Required]
    public int? CategoryId { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Le vendeur est obligatoire.")]
    public int? SellerId { get; set; }

    [StringLength(50)]
    public string? Brand { get; set; }

    [StringLength(50)]
    public string? Model { get; set; }

    [StringLength(50)]
    public string? Sku { get; set; } // Code produit unique

    [Range(0, double.MaxValue)]
    public decimal Weight { get; set; }

    [StringLength(20)]
    public string? Dimensions { get; set; }

    [StringLength(50)]
    public string? Color { get; set; } // Couleur principale

    [StringLength(20)]
    public string? Size { get; set; } // Taille (S, M, L, XL, etc.)

    [StringLength(50)]
    public string? Material { get; set; } // Matériau principal

    [StringLength(100)]
    public string? Manufacturer { get; set; } // Fabricant

    [StringLength(50)]
    public string? CountryOfOrigin { get; set; } // Pays d'origine

    [StringLength(20)]
    public string? Condition { get; set; } = "Neuf"; // État du produit

    [Range(0, 100)]
    public int? WarrantyMonths { get; set; } // Garantie en mois

    [StringLength(500)]
    public string? Features { get; set; } // Caractéristiques principales

    [StringLength(200)]
    public string? Tags { get; set; } // Tags séparés par des virgules

    public decimal? CompareAtPrice { get; set; } // Prix de comparaison (prix barré)

    public DateTime? AvailableFrom { get; set; } = DateTime.UtcNow; // Date de disponibilité

    public int? MinOrderQuantity { get; set; } = 1; // Quantité minimum de commande

    public int? MaxOrderQuantity { get; set; } // Quantité maximum de commande

    public bool RequiresShipping { get; set; } = true; // Nécessite livraison

    public bool IsDigital { get; set; } = false; // Produit numérique

    public bool IsActive { get; set; } = true;
    public bool IsFeatured { get; set; }

    public List<IFormFile> Images { get; set; } = new();
    public List<CreateProductVariantRequest> Variants { get; set; } = new();
    public List<CreateProductAttributeRequest> Attributes { get; set; } = new();
}
