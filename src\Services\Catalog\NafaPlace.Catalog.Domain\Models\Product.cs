using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Common;
using NafaPlace.Catalog.Domain.Enums;

namespace NafaPlace.Catalog.Domain.Models;

public class Product : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(500)]
    public required string Description { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal Price { get; set; }

    public int CategoryId { get; set; }

    [Required]
    [Range(0, int.MaxValue)]
    public int StockQuantity { get; set; }

    [Required]
    [MaxLength(3)]
    public required string Currency { get; set; } = "GNF"; // Franc Guinéen par défaut

    [MaxLength(50)]
    public string? Brand { get; set; }

    [MaxLength(50)]
    public string? Model { get; set; }

    [MaxLength(50)]
    public virtual string? Sku { get; set; } // Code produit unique

    [Range(0, double.MaxValue)]
    public decimal Weight { get; set; } // En kilogrammes

    [MaxLength(50)]
    public string? Dimensions { get; set; } // Format : "LxWxH" en cm

    [MaxLength(50)]
    public virtual string? Color { get; set; } // Couleur principale

    [MaxLength(20)]
    public virtual string? Size { get; set; } // Taille (S, M, L, XL, etc.)

    [MaxLength(50)]
    public virtual string? Material { get; set; } // Matériau principal

    [MaxLength(100)]
    public virtual string? Manufacturer { get; set; } // Fabricant

    [MaxLength(50)]
    public virtual string? CountryOfOrigin { get; set; } // Pays d'origine

    [MaxLength(20)]
    public string? Condition { get; set; } = "Neuf"; // État du produit

    [Range(0, 100)]
    public virtual int? WarrantyMonths { get; set; } // Garantie en mois

    [MaxLength(500)]
    public virtual string? Features { get; set; } // Caractéristiques principales

    [MaxLength(200)]
    public virtual string? Tags { get; set; } // Tags séparés par des virgules

    public virtual decimal? CompareAtPrice { get; set; } // Prix de comparaison (prix barré)

    public DateTime? AvailableFrom { get; set; } // Date de disponibilité

    public int? MinOrderQuantity { get; set; } = 1; // Quantité minimum de commande

    public virtual int? MaxOrderQuantity { get; set; } // Quantité maximum de commande

    public bool RequiresShipping { get; set; } = true; // Nécessite livraison

    public virtual bool IsDigital { get; set; } = false; // Produit numérique

    [Range(0, 5)]
    public decimal Rating { get; set; }

    public bool IsActive { get; set; } = true;
    public virtual bool IsFeatured { get; set; }

    // Statut d'approbation du produit
    public ProductApprovalStatus ApprovalStatus { get; set; } = ProductApprovalStatus.Pending;

    [MaxLength(500)]
    public string? RejectionReason { get; set; }

    public DateTime? ApprovedAt { get; set; }

    [MaxLength(50)]
    public string? ApprovedBy { get; set; }

    [Required]
    public int SellerId { get; set; }

    public Category? Category { get; set; }
    public Seller? Seller { get; set; }
    public List<ProductImage> Images { get; set; } = new();
    public List<ProductVariant> Variants { get; set; } = new();
    public List<ProductAttribute> Attributes { get; set; } = new();

    public new DateTime CreatedAt { get; set; }
    public new DateTime UpdatedAt { get; set; }
}
